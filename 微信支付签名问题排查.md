# 微信支付APP调用签名验证失败问题排查

## 问题现象
- 后端返回签名：`A99F86E353DE2EBC0F9F02BEACAAE689`
- 正确签名应该是：`42DF2B6B79DB680BABA1BCF523B115DB`
- 前端APP调用微信支付时提示"支付验证签名失败"

## 签名参数
```
appid: wx5e2f07d307654aed
noncestr: a0e66dc82a4e484d8e53df5b589a9351
package: Sign=WXPay
partnerid: 1648027588
prepayid: wx03141651248204edbe831d850be5bc0000
timestamp: 1754204368
```

## 正确的签名流程

### 1. 参数排序（按ASCII码排序）
```
appid=wx5e2f07d307654aed
noncestr=a0e66dc82a4e484d8e53df5b589a9351
package=Sign=WXPay
partnerid=1648027588
prepayid=wx03141651248204edbe831d850be5bc0000
timestamp=1754204368
```

### 2. 拼接签名字符串
```
appid=wx5e2f07d307654aed&noncestr=a0e66dc82a4e484d8e53df5b589a9351&package=Sign=WXPay&partnerid=1648027588&prepayid=wx03141651248204edbe831d850be5bc0000&timestamp=1754204368&key=6ea19f1af5a476cd249adf884922c0b6
```

### 3. MD5加密并转大写
```
MD5("appid=wx5e2f07d307654aed&noncestr=a0e66dc82a4e484d8e53df5b589a9351&package=Sign=WXPay&partnerid=1648027588&prepayid=wx03141651248204edbe831d850be5bc0000&timestamp=1754204368&key=6ea19f1af5a476cd249adf884922c0b6").toUpperCase()
= 42DF2B6B79DB680BABA1BCF523B115DB
```

## 后端可能的问题

### 1. API密钥错误
- 检查后端使用的API密钥是否为：`6ea19f1af5a476cd249adf884922c0b6`
- 确认是APIv2密钥，不是APIv3密钥

### 2. 参数名称错误
常见错误：
- `nonceStr` vs `noncestr` (大小写)
- `partnerId` vs `partnerid` (大小写)
- `prepayId` vs `prepayid` (大小写)
- `timeStamp` vs `timestamp` (大小写)

### 3. 参数值错误
- timestamp必须是字符串类型，不能是数字
- package值必须是 "Sign=WXPay"
- 所有参数值不能包含空格或特殊字符

### 4. 签名算法错误
- 必须使用MD5算法
- 结果必须转为大写
- 字符编码必须是UTF-8

### 5. 参数排序错误
- 必须按ASCII码排序
- 空值参数要排除

## 建议的修复方案

### 方案1：使用正确的签名
直接使用计算出的正确签名：
```json
{
  "appid": "wx5e2f07d307654aed",
  "partnerid": "1648027588", 
  "prepayid": "wx03141651248204edbe831d850be5bc0000",
  "package": "Sign=WXPay",
  "noncestr": "a0e66dc82a4e484d8e53df5b589a9351",
  "timestamp": "1754204368",
  "sign": "42DF2B6B79DB680BABA1BCF523B115DB"
}
```

### 方案2：修复后端签名算法
检查后端代码中的以下几点：
1. API密钥是否正确
2. 参数名称大小写是否正确
3. 参数排序是否正确
4. MD5算法实现是否正确
5. 字符编码是否为UTF-8

### 方案3：后端代码示例（Java）
```java
public String generateSign(Map<String, String> params, String apiKey) {
    // 1. 排序参数
    Map<String, String> sortedParams = new TreeMap<>(params);
    
    // 2. 拼接字符串
    StringBuilder sb = new StringBuilder();
    for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
        if (entry.getValue() != null && !entry.getValue().isEmpty()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
    }
    sb.append("key=").append(apiKey);
    
    // 3. MD5加密
    String sign = DigestUtils.md5Hex(sb.toString()).toUpperCase();
    return sign;
}
```

## 验证方法
使用提供的JavaScript或Python代码验证签名是否正确。

## 注意事项
1. 微信支付对签名验证非常严格，任何细微差别都会导致失败
2. 建议在后端添加详细的签名生成日志，便于调试
3. 确保所有参数的数据类型和格式完全符合微信支付要求
