// 修复后的OrderServiceImpl.java中的支付方法

/**
 * 修复后的APP微信支付方法
 */
public ResponseObject orderPayFixed(HttpServletRequest request, OrderPayDto orderPayDto) {
    Integer userId = (Integer) request.getAttribute("userId");
    String isApp = request.getHeader("isApp");
    log.info("用户{} 支付订单：{} 优惠卷：{}, 类型：{}, isApp:{}", userId, orderPayDto.getOrderId(), orderPayDto.getCouponId(), orderPayDto.getType(), isApp);
    
    ImsOrder order = orderListMapper.selectByIdAndUserId(orderPayDto.getOrderId(), userId);
    if (order == null) {
        return ResponseObject.fail("订单信息不存在");
    }

    if (order.getPayType() < 1) {
        return ResponseObject.fail("师傅已取消接单，当前订单状态不可被支付");
    }

    ImsCoach coachInfo = coachListMapper.selectByPrimaryKey(order.getCoachId());
    if (coachInfo == null) {
        return ResponseObject.fail("尚未查询到接单师傅信息，请再次核实");
    }
    if (coachInfo.getIsEnable() == 2) {
        return ResponseObject.fail("由于当前师傅违反平台规定，接单状态已被关闭，请取消当前订单重新下单");
    }

    // 获取优惠券折扣金额
    BigDecimal discount = null;
    if (orderPayDto.getCouponId() != null && orderPayDto.getCouponId() > 0) {
        ResponseObject cd = getCouponDiscount(orderPayDto.getCouponId(), order.getPayPrice(), userId);
        if (!"200".equals(cd.getCode())) {
            return cd;
        }
        discount = BigDecimal.valueOf((Double) cd.getData());
        log.info("获取优惠金额：{}", discount);
    }

    BigDecimal payPrice = order.getPayPrice();
    if (discount != null) {
        BigDecimal price = order.getPayPrice().subtract(discount);
        payPrice = price.compareTo(BigDecimal.ZERO) < 0
                ? BigDecimal.valueOf(0.01)
                : price;
    }
    log.info("优惠前：{}, 优惠后：{}", order.getPayPrice(), payPrice);

    // 微信支付
    if (orderPayDto.getType() == 1) {
        ImsUser userList = userListMapper.selectByPrimaryKey(userId);
        
        if ("0".equals(isApp)) {
            // 小程序支付（保持原有逻辑）
            WxAppPayReq req = new WxAppPayReq();
            req.setOpenId(userList.getWxOpenid());
            req.setOrderCode(order.getOrderCode());
            req.setPayPrice(payPrice);
            req.setAttach("Massage-" + orderPayDto.getCouponId());

            try {
                UnifiedOrderRes unifiedOrderRes = payService.wxAppPay(req, request.getRemoteAddr());
                OrderPayVo vo = new OrderPayVo();
                vo.setAppId(unifiedOrderRes.getAppid());
                long timestamp = System.currentTimeMillis() / 1000;
                vo.setTimestamp(timestamp);
                String nonceStr = payService.genNonceStr();
                vo.setNonceStr(nonceStr);
                vo.setPrepayId(unifiedOrderRes.getPrepayId());
                vo.setPackageValue("Sign=WXPay");
                vo.setPartnerId(mchId);

                // 小程序支付签名参数（驼峰格式）
                SortedMap<String, String> signParams = new TreeMap<>();
                signParams.put("appId", unifiedOrderRes.getAppid());
                signParams.put("timeStamp", String.valueOf(timestamp));
                signParams.put("nonceStr", nonceStr);
                signParams.put("package", "prepay_id=" + unifiedOrderRes.getPrepayId());
                signParams.put("signType", "MD5");
                String paySign = payService.buildSign(signParams);

                vo.setSign(paySign);
                return ResponseObject.ok(vo);
            } catch (Exception e) {
                log.error("支付失败", e);
                throw new RuntimeException(e);
            }
        } else if ("1".equals(isApp)) {
            // APP支付（修复后的逻辑）
            WxAppPayReq req = new WxAppPayReq();
            req.setOrderCode(order.getOrderCode());
            req.setPayPrice(payPrice);
            req.setAttach("Massage-" + orderPayDto.getCouponId());
            
            try {
                // 1. 调用微信APP下单接口，获取prepay_id
                UnifiedOrderRes unifiedOrderRes = payService.wxAppPayForApp(req, request.getRemoteAddr());
                OrderPayVo vo = new OrderPayVo();
                vo.setAppId(unifiedOrderRes.getAppid());
                long timestamp = System.currentTimeMillis() / 1000;
                vo.setTimestamp(timestamp);
                String nonceStr = payService.genNonceStr();
                vo.setNonceStr(nonceStr);
                vo.setPrepayId(unifiedOrderRes.getPrepayId());
                vo.setPackageValue("Sign=WXPay");
                vo.setPartnerId(mchId);

                // 2. 使用正确的APP支付签名算法
                String apiKey = "6ea19f1af5a476cd249adf884922c0b6"; // 从配置文件读取
                String correctSign = WeChatPaySignatureUtil.generateAppPaySignCorrect(
                    unifiedOrderRes.getAppid(),
                    nonceStr,
                    unifiedOrderRes.getPrepayId(),
                    mchId,
                    timestamp,
                    apiKey
                );

                vo.setSign(correctSign);
                
                log.info("APP微信支付参数生成成功");
                log.info("appId: {}", unifiedOrderRes.getAppid());
                log.info("timestamp: {}", timestamp);
                log.info("nonceStr: {}", nonceStr);
                log.info("prepayId: {}", unifiedOrderRes.getPrepayId());
                log.info("partnerId: {}", mchId);
                log.info("sign: {}", correctSign);
                
                return ResponseObject.ok(vo);
            } catch (Exception e) {
                log.error("APP微信支付下单失败", e);
                throw new RuntimeException(e);
            }
        }
    }
    return ResponseObject.fail("支付失败");
}

/**
 * 修复后的差价支付方法
 */
public ResponseObject payDiffPriceFixed(HttpServletRequest request, PayDiffPriceDto payDiffPriceDto) {
    Integer userId = (Integer) request.getAttribute("userId");
    ImsUser userInfo = userListMapper.selectByPrimaryKey(userId);
    ImsOrderDiffPrice imsOrderDiffPrice = orderDiffPriceMapper.selectByPrimaryKey(payDiffPriceDto.getId());
    
    if (!imsOrderDiffPrice.getUserId().equals(userId)) {
        return ResponseObject.fail("该加价订单不属于你，你无权操作");
    }
    if (imsOrderDiffPrice.getStatus() == 2) {
        return ResponseObject.fail("此订单已支付，无需重复支付");
    }
    
    ImsOrder orderInfo = orderListMapper.selectByPrimaryKey(imsOrderDiffPrice.getOrderId());
    if (orderInfo.getPayType() < 3) {
        return ResponseObject.fail("当前订单状态不允许付款");
    }

    // 微信支付通道
    if (payDiffPriceDto.getType() == 1) {
        WxAppPayReq req = new WxAppPayReq();
        req.setOpenId(userInfo.getWxOpenid());
        req.setOrderCode(imsOrderDiffPrice.getDiffCode());
        req.setPayPrice(imsOrderDiffPrice.getDiffAmount());
        req.setAttach("diffPayment");
        
        OrderPayVo vo = new OrderPayVo();
        try {
            UnifiedOrderRes unifiedOrderRes = payService.wxAppPay(req, request.getRemoteAddr());
            vo.setAppId(unifiedOrderRes.getAppid());
            long timestamp = System.currentTimeMillis() / 1000;
            vo.setTimestamp(timestamp);
            String nonceStr = payService.genNonceStr();
            vo.setNonceStr(nonceStr);
            vo.setPrepayId(unifiedOrderRes.getPrepayId());
            vo.setPackageValue("Sign=WXPay");
            vo.setPartnerId(mchId);

            // 使用正确的签名算法
            String apiKey = "6ea19f1af5a476cd249adf884922c0b6"; // 从配置文件读取
            String correctSign = WeChatPaySignatureUtil.generateAppPaySignCorrect(
                unifiedOrderRes.getAppid(),
                nonceStr,
                unifiedOrderRes.getPrepayId(),
                mchId,
                timestamp,
                apiKey
            );
            
            vo.setSign(correctSign);
            log.info("差价支付调用成功----等待支付回调接口确认");
            log.info("用户：{} 缴纳订单差价:{},拼成的商户code是:{}", userId, imsOrderDiffPrice.getCreditPrice(), imsOrderDiffPrice.getDiffCode());
            return ResponseObject.ok(vo);
        } catch (Exception e) {
            log.error("差价支付失败", e);
            return ResponseObject.fail("支付失败", e.getMessage());
        }
    }
    return ResponseObject.fail("尚未当前适配的支付方式");
}
