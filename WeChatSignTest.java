import java.security.MessageDigest;
import java.util.*;

public class WeChatSignTest {
    
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    public static String generateSign(Map<String, String> params, String apiKey) {
        // 1. 排序参数
        Map<String, String> sortedParams = new TreeMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (value != null && !value.isEmpty() && !"sign".equals(key)) {
                sortedParams.put(key, value);
            }
        }
        
        // 2. 拼接字符串
        StringBuilder stringA = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            stringA.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        
        // 3. 拼接API密钥
        String stringSignTemp = stringA.toString() + "key=" + apiKey;
        
        // 4. MD5加密并转大写
        String sign = md5(stringSignTemp);
        
        System.out.println("签名原始字符串: " + stringSignTemp);
        System.out.println("生成的签名: " + sign);
        
        return sign;
    }
    
    public static void main(String[] args) {
        String apiKey = "6ea19f1af5a476cd249adf884922c0b6";
        
        // 测试最新数据
        Map<String, String> params = new HashMap<>();
        params.put("appid", "wx5e2f07d307654aed");
        params.put("noncestr", "60314386a74f4bc1ac7a36fa85f58cac");
        params.put("package", "Sign=WXPay");
        params.put("partnerid", "1648027588");
        params.put("prepayid", "wx03141651248204edbe831d850be5bc0000");
        params.put("timestamp", "1754206596");
        
        String correctSign = generateSign(params, apiKey);
        
        System.out.println("=== 测试结果 ===");
        System.out.println("后端返回签名: A5F3F8151C5BE151B38B27718CA5C1A7");
        System.out.println("正确签名应该是: " + correctSign);
        
        // 验证
        boolean isValid = "A5F3F8151C5BE151B38B27718CA5C1A7".equals(correctSign);
        System.out.println("签名验证结果: " + isValid);
        
        if (!isValid) {
            System.out.println("❌ 后端签名算法有误！");
        } else {
            System.out.println("✅ 后端签名正确！");
        }
    }
}
