package com.ims.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ims.admin.exception.ParamException;
import com.ims.common.CacheService;
import com.ims.common.PageResult;
import com.ims.common.ResponseObject;
import com.ims.config.FixedWindowRateConfig;
import com.ims.dto.*;
import com.ims.mapper.*;
import com.ims.utils.*;
import com.ims.vo.*;
import com.ims.wx.pay.dto.UnifiedOrderRes;
import com.ims.wx.pay.dto.WxAppPayReq;
import com.ims.wx.pay.service.PayService;
import com.ims.po.*;
import com.ims.service.OrderService;
import com.ims.service.UserService;
import com.ims.wx.push.common.TemplateData;
import com.ims.wx.push.common.WxSubscribeTemplate;
import com.ims.wx.push.service.PushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Resource
    private ImsOrderMapper orderListMapper;

    @Resource
    private ImsServicePriceSettingMapper priceSettingMapper;

    @Resource
    private ImsServicePriceSettingOrderMapper priceSettingOrderMapper;

    @Resource
    private ImsCoachMapper coachListMapper;

    @Resource
    private ImsCouponRecordMapper couponRecordMapper;

    @Resource
    private ImsCouponMapper couponMapper;

    @Resource
    private ImsOrderAftermarketMapper orderAftermarketMapper;

    @Resource
    private ImsOrderGoodsMapper orderGoodsListMapper;

    @Resource
    private ImsServiceListMapper serviceListMapper;

    @Resource
    private ImsFinanceLogMapper financeLogMapper;

    @Resource
    private ImsOrderCommissionMapper orderCommissionMapper;

    @Resource
    private ImsUserMapper userListMapper;

    @Resource
    private ImsShequshopSchoolAdminMapper schoolAdminMapper;

    @Resource
    private ImsOrderAddressMapper orderAddressMapper;

    @Resource
    private ImsRefundOrderMapper refundOrderMapper;

    @Resource
    private ImsRefundOrderGoodsMapper refundOrderGoodsMapper;
    @Resource
    private ImsAddressMapper addressMapper;

    @Resource
    private ImsQuotedPriceMapper quotedPriceMapper;

    @Resource
    private UserService userService;

    @Resource
    private CacheService cacheService;

    @Resource
    private PayService payService;

    @Resource
    private PushService pushService;

    @Resource
    private ImsOrderReadStatusMapper readStatusMapper;

    @Resource
    @Qualifier("pushExecutor")
    private ThreadPoolTaskExecutor pushExecutor;

    @Resource
    private ImsOrderCommentMapper orderCommentMapper;

    @Resource
    private ImsOrderCommentGoodsMapper commentGoodsMapper;

    @Resource
    private ImsOrderAftermarketMapper aftermarketMapper;

    @Resource
    private ImsCarMapper imsCarMapper;

    @Resource
    private ImsPartnerMapper partnerMapper;

    @Resource
    private ImsOrderDiffPriceMapper orderDiffPriceMapper;

    @Resource
    private ImsOrderDiffPaymentMapper orderDiffPaymentMapper;

    @Resource
    private ImsOrderDiffPriceRefundMapper diffPriceRefundMapper;

    @Resource
    private ImsCreditDetailMapper creditDetailMapper;

    //加分配置
    private final Integer ADD_CREDIT = 1;

    //差价是否参与分销。1参与 0不参与
    private final Byte OPEN_DIFF_STATUS = 1;


    @Value("${wx.pay.mchId}")
    private String mchId;

    @Value("${wx.pay.notify.url}")
    private String notifyUrl;


    // 限制并发推送任务数，避免线程池击穿
    private final Semaphore pushSemaphore = new Semaphore(10);
    // 限制每秒最多推送 5 次，避免微信接口限流
    // 自定义固定窗口限流：5次/秒
    private final FixedWindowRateConfig rateLimiter = new FixedWindowRateConfig(5, 1000);

    @Override
    public OrderInfoVo orderInfo(Integer userId, int orderId, boolean isAdmin) {
        OrderInfoVo orderVo = new OrderInfoVo();
        //订单信息
        ImsOrder orderList = new ImsOrder();
        if (isAdmin) {
            orderList = orderListMapper.selectByPrimaryKey(orderId);
            userId = orderList.getUserId();
        } else {
            orderList = orderListMapper.selectByIdAndUserId(orderId, userId);
        }
        if (orderList == null) {
            throw new ParamException("查无此订单");
        }

        //规格
//        List<ImsServicePriceSetting> priceSetting = priceSettingMapper.selectByOrderId(orderId);

        //师傅信息
        if (orderList.getCoachId() != null && orderList.getCoachId() > 0) {
            ImsCoach coachInfo = coachListMapper.selectByPrimaryKey(orderList.getCoachId());
            if (coachInfo != null && coachInfo.getSelfImg() != null && !coachInfo.getSelfImg().isEmpty()) {
                String[] selfImg = coachInfo.getSelfImg().split(",");
                // 获取第一个 URL 字符串
                String selfImgResult = selfImg[0];
                coachInfo.setSelfImg(selfImgResult);
            }
            if (orderList.getPayType() == 1 || orderList.getPayType() == -2 || orderList.getPayType() == -1 || orderList.getPayType() == -3) {
                coachInfo.setMobile(ConfidentialUtils.maskPhone(coachInfo.getMobileEncry()));
                coachInfo.setCoachName(ConfidentialUtils.maskPhone(coachInfo.getCoachName()));
            }
            orderVo.setCoachInfo(coachInfo);
        }

        //优惠卷信息
        if (orderList.getCouponId() != null) {
            ImsCouponRecord couponInfo = couponRecordMapper.selectByPrimaryKey(orderList.getCouponId());
            orderVo.setCouponInfo(couponInfo);
        }

        //售后服务信息
        ImsOrderAftermarket aftermarketInfo = orderAftermarketMapper.selectByOrderId(orderId);

        //商品详情
        List<ImsOrderGoods> orderGoodsList = orderGoodsListMapper.selectByUserIdAndOrderId(userId, orderId);
        for (ImsOrderGoods orderGoods : orderGoodsList) {
            if (orderGoods.getGoodsId() != null) {
                ImsServiceList serviceList = serviceListMapper.selectByPrimaryKey(orderGoods.getGoodsId());
                orderGoods.setGoodsInfo(serviceList);
            }
            //carId>0，根据购物车id查  否则根据订单id查
            if (orderGoods.getCarId() == 0) {
                List<ImsServicePriceSetting> priceSetting = priceSettingMapper.selectByOrderIdAndCarId(orderId, 0);
                orderGoods.setPriceSetting(priceSetting);
            } else {
                List<ImsServicePriceSetting> priceSetting = priceSettingMapper.selectByOrderIdAndCarId(orderId, orderGoods.getCarId());

                // 假设allList有N*settingNum条
                Map<Integer, List<ImsServicePriceSetting>> settingOrderMap = new LinkedHashMap<>();
                for (ImsServicePriceSetting order : priceSetting) {
                    settingOrderMap.computeIfAbsent(order.getId(), k -> new ArrayList<>()).add(order);
                }
                List<ImsServicePriceSetting> result = new ArrayList<>();
                for (Map.Entry<Integer, List<ImsServicePriceSetting>> entry : settingOrderMap.entrySet()) {
                    List<ImsServicePriceSetting> orders = entry.getValue();
                    if (orders.isEmpty()) continue;
                    // 合并val，去重，不同用逗号拼接
                    String mergedVal = orders.stream()
                            .map(ImsServicePriceSetting::getVal)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.joining(","));
                    // 用第一个对象为模板
                    ImsServicePriceSetting mergedOrder = orders.get(0);
                    mergedOrder.setVal(mergedVal);
                    result.add(mergedOrder);
                }
                orderGoods.setPriceSetting(result);
            }
        }

        //地址
        ImsOrderAddress orderAddress = orderAddressMapper.selectByOrderId(orderId);

        BeanUtil.copyProperties(orderList, orderVo);
//        orderVo.setPriceSetting(priceSetting);
        orderVo.setAftermarketInfo(aftermarketInfo);
        orderVo.setOrderGoods(orderGoodsList);
        orderVo.setCanRefund(canRefund(Integer.valueOf(orderList.getPayType())));
        orderVo.setAddressInfo(orderAddress);

        return orderVo;
    }

    @Override
    @Transactional
    public ResponseObject updateOrder(HttpServletRequest request, OrderDto orderDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        ImsOrder order = orderListMapper.selectByIdAndUserId(orderDto.getOrderId(), userId);
        if (order == null) {
            return ResponseObject.fail("订单不存在");
        }

        // 检查订单状态
        if (order.getPayType() == 7) {
            return ResponseObject.fail("此订单已确认完成过了",-1);
        }
        if (order.getPayType() != 6) {
            return ResponseObject.fail("不在服务中不能确认完成",-1);
        }
        if (!userId.equals(order.getUserId())) {
            log.info("当前用户为:{} 订单的用户为:{}",userId,order.getUserId());
            return ResponseObject.fail("您没有操作此订单的权限");
        }
        // 更新订单状态为完成
        order.setPayType((byte) 7);
        orderListMapper.updatePayTypeById(order.getId(), order.getPayType());

        ServiceConfigVo schoolConfig = (ServiceConfigVo) cacheService.get("schoolConfig");
        BigDecimal coachPrice;
        BigDecimal price = BigDecimal.ZERO;
        // 财务日志与师傅相关操作
        if (order.getCoachId() != null) {
//            //计算师傅应得金额
//            if (order.getType() == 1) {
//                // 比例模式： payPrice / ((commissionRatio + 100) / 100)
//                BigDecimal divisor = BigDecimal.valueOf(schoolConfig.getCommissionRatio())
//                        .add(BigDecimal.valueOf(100))
//                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
//                coachPrice = order.getPayPrice().divide(divisor, 2, RoundingMode.DOWN);
//            } else {
//                // 固定模式： payPrice * (100 - commissionRatio) / 100
//                coachPrice = order.getPayPrice()
//                        .multiply(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(schoolConfig.getCommissionRatio())))
//                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
//            }

            //如果 type==0 且 coachPrice <= 0.01，则用 payPrice 代替
//            price = coachPrice;
            price = order.getCoachServicePrice();
            if (order.getType() == 0 && price.compareTo(BigDecimal.valueOf(0.01)) <= 0) {
                coachPrice = order.getPayPrice();
                price = coachPrice;
            }

            //当前订单补差价金额
            BigDecimal diffPrice = BigDecimal.ZERO;
            //查询当前订单是否有补差价订单，获取补差价订单的补价金额
            List<ImsOrderDiffPrice> imsOrderDiffPrices = orderDiffPriceMapper.selectCoachPriceByOrderId(order.getId());
            if (imsOrderDiffPrices != null && !imsOrderDiffPrices.isEmpty()) {
                for (ImsOrderDiffPrice orderDiffPrice : imsOrderDiffPrices) {
                    diffPrice.add(orderDiffPrice.getCreditPrice());
                }
            }

            //如果师傅报价0.01，则师傅金额直接为0.01，平台不再抽取
            BigDecimal coachServicePrice = order.getCoachServicePrice() == null ? BigDecimal.ZERO : order.getCoachServicePrice();
            if (order.getType() == 1 && coachServicePrice.compareTo(BigDecimal.valueOf(0.01)) <= 0) {
                price = BigDecimal.valueOf(0.01);
                log.info("进入师傅报价0.01中-----------当前订单师傅报价金额为{}",coachServicePrice);
            }

            ImsCoach coachList = coachListMapper.selectByPrimaryKey(order.getCoachId());
            if (coachList.getId()==null){
                return ResponseObject.fail("当前师傅已不存在");
            }
            //往订单阅读状态表中插入数据
            ImsOrderReadStatus readStatusUser = new ImsOrderReadStatus();
            readStatusUser.setOrderId(order.getId());
            readStatusUser.setUserId(userId);
            readStatusUser.setRole(1);
            readStatusUser.setPayType(7);
            readStatusUser.setReadFlag(0);
            readStatusUser.setCreateTime(DateTime.now());
            readStatusMapper.insertSelective(readStatusUser);

            ImsOrderReadStatus readStatusCoach = new ImsOrderReadStatus();
            readStatusCoach.setOrderId(order.getId());
            readStatusCoach.setUserId(coachList.getId());
            readStatusCoach.setRole(2);
            readStatusCoach.setPayType(7);
            readStatusCoach.setReadFlag(0);
            readStatusCoach.setCreateTime(DateTime.now());
            readStatusMapper.insertSelective(readStatusCoach);
            //师傅服务次数+1
            coachList.setCount(coachList.getCount() + 1);

            //给师傅加信誉分
            Integer credit = coachList.getCredit();
            ImsCreditDetail creditDetail = new ImsCreditDetail();
            creditDetail.setCoachId(coachList.getId());
            creditDetail.setCreditType((byte) 1);
            creditDetail.setPoints(ADD_CREDIT);
            creditDetail.setBeforePoints(credit);
            creditDetail.setAfterPoints(credit + 1);
            creditDetail.setChangeReason((byte) 1);
            creditDetail.setOrderId(order.getId());
            creditDetail.setOperatorType((byte) 1);
            creditDetail.setCreateTime(new Date());
            creditDetailMapper.insertSelective(creditDetail);

            if(coachList.getServicePrice()==null){
                coachList.setServicePrice(BigDecimal.ZERO);
            }
            //增加师傅信誉分
            coachList.setCredit(credit + 1);
            //给师傅加钱：servicePrice += coachPrice   给师傅加差价金额 + diffPrice
            BigDecimal add = coachList.getServicePrice().add(price).add(diffPrice);
            coachList.setServicePrice(add);
            coachListMapper.updateByPrimaryKeySelective(coachList);

            //如果后台开启加价金额参与分销，则服务费加上分销金额
            if (OPEN_DIFF_STATUS == 1) {
                price = price.add(diffPrice);
            }

            //查询当前师傅是否有上级代理，如果有则给他上级代理代理金额加上平台代理比例
            ImsUser userList = userListMapper.selectByPrimaryKey(coachList.getUserId());
            if (userList == null) {
                return ResponseObject.fail("暂无该师傅用户信息");
            }
            if (userList.getPid() != null) {
                ImsUser pidUser = userListMapper.selectByPrimaryKey(userList.getPid());
                if (pidUser != null) {
                    ImsPartner imsPartnerPid = partnerMapper.selectByUserId(pidUser.getId());
                    BigDecimal topPrice;
                    if (imsPartnerPid != null) {
                        BigDecimal commissionRate1 = imsPartnerPid.getCommissionRate1();
                        topPrice = price.multiply(commissionRate1)
                                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)
                                        .setScale(2, RoundingMode.HALF_UP);

                    } else {
                        topPrice = price.multiply(BigDecimal.valueOf(schoolConfig.getCashOne())
                                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                        .setScale(2, RoundingMode.HALF_UP);
                    }
                    if (pidUser.getNewCash() == null) {
                        pidUser.setNewCash(BigDecimal.ZERO);
                    }
                    pidUser.setNewCash(pidUser.getNewCash().add(topPrice));
                    //更新当前用户金额
                    log.info("当前下单用户为：{}，接单师傅为：{}，用户付款金额为：{}，师傅报价金额为：{}，师傅根据用户支付算出的金额为：{}，师傅上级为：{}，上级目前金额为：{}，上级到账金额为：{}",
                            userId, coachList.getId(), order.getPayPrice(), order.getCoachServicePrice(), price, pidUser.getId(), pidUser.getNewCash(), topPrice);
                    userListMapper.updateByPrimaryKeySelective(pidUser);
                    log.info("当前已更新user表，上级人员newCash人员为：{}，更新金额为{}", pidUser, topPrice);


                    ImsUser ppidUser = userListMapper.selectByPrimaryKey(pidUser.getPid());
                    if (ppidUser != null) {
                        ImsPartner imsPartner = partnerMapper.selectByUserId(ppidUser.getId());
                        if (imsPartner != null) {
                            log.info("师傅：{}, 上级id：{}, 上上级id:{}是vip用户", userList.getId(), pidUser.getId(), ppidUser.getId());
                            //把孙子节点的单当子节点分佣
                            if (ppidUser.getNewCash() == null) {
                                ppidUser.setNewCash(BigDecimal.ZERO);
                            }
                            BigDecimal bigDecimal = price.multiply(imsPartner.getCommissionRate2())
                                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)
                                    .setScale(2, RoundingMode.HALF_UP);
                            ppidUser.setNewCash(pidUser.getNewCash().add(bigDecimal));
                            userListMapper.updateByPrimaryKeySelective(ppidUser);

                            //落库佣金明细记录表中order_commission
                            ImsOrderCommission massageServiceOrderCommission = new ImsOrderCommission();
                            massageServiceOrderCommission.setUserId(coachList.getUserId());
                            massageServiceOrderCommission.setTopId(pidUser.getPid());
                            massageServiceOrderCommission.setOrderId(order.getId());
                            massageServiceOrderCommission.setOrderCode(order.getOrderCode());
                            massageServiceOrderCommission.setCash(topPrice.doubleValue());
                            massageServiceOrderCommission.setSecondCash(BigDecimal.valueOf(0.00));
                            massageServiceOrderCommission.setCreateTime(new Date());
                            massageServiceOrderCommission.setStatus(0);
                            massageServiceOrderCommission.setAdminId(order.getAdminId() == null ? 0 : order.getAdminId());
                            massageServiceOrderCommission.setCashTime(new Date());
                            massageServiceOrderCommission.setType(4);
                            orderCommissionMapper.insertSelective(massageServiceOrderCommission);
                            //落库财务记录
                            ImsFinanceLog coachFinanceLogOne = new ImsFinanceLog();
                            coachFinanceLogOne.setType((byte) 1);
                            coachFinanceLogOne.setUserId(pidUser.getPid());
                            coachFinanceLogOne.setPrice(topPrice);
                            coachFinanceLogOne.setCoachId(0);
                            coachFinanceLogOne.setAgentId(0);
                            coachFinanceLogOne.setCreateTime(new Date());
                            financeLogMapper.insert(coachFinanceLogOne);

                        }
                    }



                    //落库佣金明细记录表中order_commission
                    ImsOrderCommission massageServiceOrderCommission = new ImsOrderCommission();
                    massageServiceOrderCommission.setUserId(coachList.getUserId());
                    massageServiceOrderCommission.setTopId(userList.getPid());
                    massageServiceOrderCommission.setOrderId(order.getId());
                    massageServiceOrderCommission.setOrderCode(order.getOrderCode());
                    massageServiceOrderCommission.setCash(topPrice.doubleValue());
                    massageServiceOrderCommission.setSecondCash(BigDecimal.valueOf(0.00));
                    massageServiceOrderCommission.setCreateTime(new Date());
                    massageServiceOrderCommission.setStatus(0);
                    massageServiceOrderCommission.setAdminId(order.getAdminId() == null ? 0 : order.getAdminId());
                    massageServiceOrderCommission.setCashTime(new Date());
                    massageServiceOrderCommission.setType(4);
                    orderCommissionMapper.insertSelective(massageServiceOrderCommission);
                    log.info("师傅上级金额落库commission已到账,目前账户newCash金额为：{}：", pidUser.getNewCash().add(topPrice));
                    //落库财务记录
                    ImsFinanceLog coachFinanceLogOne = new ImsFinanceLog();
                    coachFinanceLogOne.setType((byte) 1);
                    coachFinanceLogOne.setUserId(userList.getPid());
                    coachFinanceLogOne.setPrice(topPrice);
                    coachFinanceLogOne.setCoachId(0);
                    coachFinanceLogOne.setAgentId(0);
                    coachFinanceLogOne.setCreateTime(new Date());
                    financeLogMapper.insert(coachFinanceLogOne);
                    log.info("师傅上级金额落库财务日志成功");
                }
            }


            //财务记录
            ImsFinanceLog financeLog = new ImsFinanceLog();
            financeLog.setType((byte) 0);
            financeLog.setUserId(0);
            financeLog.setPrice(price.add(diffPrice));
            financeLog.setCoachId(order.getCoachId());
            financeLog.setAgentId(0);
            financeLog.setCreateTime(new Date());
            financeLogMapper.insert(financeLog);
        }
        //判断订单是否有一级分销人员
        ImsUser imsUser = userListMapper.selectByPrimaryKey(userId);
        if(imsUser != null && imsUser.getPid() != null && imsUser.getPid() != 0) {
            log.info("进入用户一级分销，当前用户为：{},当前用户上级为:{}", userId, order.getTopUid());
            //增加分销佣金记录
            //计算用户是否使用优惠券，如果使用了优惠券，则分销金额应减去优惠的金额
            if(order.getCouponId()!=null && order.getDiscount() != null && order.getCouponId()!=0 && order.getDiscount() != 0){
                BigDecimal beforePayPrice = order.getPayPrice();
                BigDecimal afterPayPrice = order.getPayPrice().subtract(BigDecimal.valueOf(order.getDiscount()))
                        .setScale(2, RoundingMode.HALF_UP);
                order.setPayPrice(afterPayPrice);
                log.info("用户：{} 优惠前支付：{}  优惠后实际支付：{}  优惠卷id：{} 优惠卷金额：{}", userId, beforePayPrice, afterPayPrice, order.getCouponId(), order.getDiscount());
            }
            ImsUser pidUser = userListMapper.selectByPrimaryKey(imsUser.getPid());
            ImsPartner imsPartner = partnerMapper.selectByUserId(pidUser.getId());
            BigDecimal cashOne;
            if (imsPartner != null) {
                cashOne = price.multiply(imsPartner.getCommissionRate1())
                        .divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
            } else {
                cashOne = price.multiply(new BigDecimal(schoolConfig.getCashOne()))
                        .divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
            }
            log.info("用户上级：{}  实际分销金额为：{}", imsUser.getPid() , cashOne.doubleValue());
            ImsFinanceLog financeLogOne = new ImsFinanceLog();
            financeLogOne.setType((byte) 1);
            financeLogOne.setUserId(order.getTopUid());
            financeLogOne.setPrice(cashOne);
            financeLogOne.setAgentId(0);
            financeLogOne.setCreateTime(new Date());
            financeLogMapper.insert(financeLogOne);
            log.info("用户一级分销入库financeLog成功，当前用户实际付款金额为：{},分销金额{}",order.getPayPrice(), cashOne.doubleValue());
            if(pidUser.getNewCash()==null){
                pidUser.setNewCash(BigDecimal.ZERO);
            }
            pidUser.setNewCash(pidUser.getNewCash().add(cashOne));
            pidUser.setLock(pidUser.getLock() == null ? 1 : pidUser.getLock() + 1);
            userListMapper.updateByPrimaryKeySelective(pidUser);
            log.info("用户一级更新用户表成功，当前用户实际付款金额为：{},分销金额{}",order.getPayPrice(),cashOne.doubleValue());

            if (cashOne.compareTo(BigDecimal.ZERO) > 0) {
                ImsUser ppidUser = userListMapper.selectByPrimaryKey(pidUser.getPid());
                if (ppidUser != null) {
                    ImsPartner imsPartner1 = partnerMapper.selectByUserId(ppidUser.getId());
                    if (imsPartner1 != null) {
                        BigDecimal cashTwo = price.multiply(imsPartner1.getCommissionRate2())
                            .divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
                        log.info("用户：{}, 上级id：{}, 上上级id:{}是vip用户", userId, order.getTopUid(), ppidUser.getId());
                        ImsOrderCommission ppidCommission = new ImsOrderCommission();
                        ppidCommission.setUserId(userId);
                        ppidCommission.setTopId(ppidUser.getId());
                        ppidCommission.setSecondUid(0);
                        ppidCommission.setOrderId(order.getId());
                        ppidCommission.setOrderCode(order.getOrderCode());
                        ppidCommission.setCash(cashTwo.doubleValue());
                        ppidCommission.setSecondCash(BigDecimal.ZERO);
                        ppidCommission.setCashTime(new Date());
                        ppidCommission.setCreateTime(new Date());
                        ppidCommission.setStatus(0);
                        ppidCommission.setType(4);
                        orderCommissionMapper.insert(ppidCommission);
                        log.info("用户上上级VIP分销入库commission成功，当前用户:{},实际付款金额为：{},分销VIP为：{}，分销金额{}", userId, order.getPayPrice(), ppidUser.getId(), cashOne.doubleValue());


                        ImsFinanceLog financeLogTwo = new ImsFinanceLog();
                        financeLogTwo.setType((byte) 1);
                        financeLogTwo.setUserId(ppidUser.getId());
                        financeLogTwo.setPrice(cashTwo);
                        financeLogTwo.setAgentId(0);
                        financeLogTwo.setCreateTime(new Date());
                        financeLogMapper.insert(financeLogTwo);
                        log.info("用户上上级VIP分销入库financeLog成功，当前用户实际付款金额为：{},分销金额{}",order.getPayPrice(), cashTwo.doubleValue());

                        if(ppidUser.getNewCash() == null){
                            ppidUser.setNewCash(BigDecimal.ZERO);
                        }
                        ppidUser.setNewCash(ppidUser.getNewCash().add(cashTwo));
                        userListMapper.updateByPrimaryKeySelective(ppidUser);
                        log.info("用户上上级VIP更新用户表成功，当前用户实际付款金额为：{},分销金额{}",order.getPayPrice(),cashTwo.doubleValue());

                    }

                }
                ImsOrderCommission commission = new ImsOrderCommission();
                commission.setUserId(userId);
                commission.setTopId(order.getTopUid());
                commission.setSecondUid(0);
                commission.setOrderId(order.getId());
                commission.setOrderCode(order.getOrderCode());
                commission.setCash(cashOne.doubleValue());
                commission.setSecondCash(BigDecimal.ZERO);
                commission.setCashTime(new Date());
                commission.setCreateTime(new Date());
                commission.setStatus(0);
                commission.setType(4);
                orderCommissionMapper.insert(commission);
                log.info("用户一级分销入库commission成功，当前用户:{},实际付款金额为：{},分销上级为：{}，分销金额{}",userId, order.getPayPrice(),order.getTopUid(), cashOne.doubleValue());
            }
        }
        return ResponseObject.ok();

    }

    @Override
    @Transactional
    public ResponseObject orderPay(HttpServletRequest request, OrderPayDto orderPayDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        String isApp = request.getHeader("isApp");
        log.info("用户{} 支付订单：{} 优惠卷：{}, 类型：{}, isApp:{}", userId, orderPayDto.getOrderId(), orderPayDto.getCouponId(), orderPayDto.getType(), isApp);
        ImsOrder order = orderListMapper.selectByIdAndUserId(orderPayDto.getOrderId(), userId);
        if (order == null) {
            return ResponseObject.fail("订单信息不存在");
        }

        if (order.getPayType()<1){
            return ResponseObject.fail("师傅已取消接单，当前订单状态不可被支付");
        }

        ImsCoach coachInfo = coachListMapper.selectByPrimaryKey(order.getCoachId());
        if (coachInfo == null) {
            return ResponseObject.fail("尚未查询到接单师傅信息，请再次核实");
        }
        if (coachInfo.getIsEnable() == 2){
            return ResponseObject.fail("由于当前师傅违反平台规定，接单状态已被关闭，请取消当前订单重新下单");
        }
        //获取优惠卷折扣金额
        BigDecimal discount = null;
        if (orderPayDto.getCouponId() != null && orderPayDto.getCouponId() > 0) {
            ResponseObject cd = getCouponDiscount(orderPayDto.getCouponId(), order.getPayPrice(), userId);
            if (!"200".equals(cd.getCode())) {
                return cd;
            }
            // cd.data = Map.of("discount", BigDecimal)
            discount = BigDecimal.valueOf((Double) cd.getData());
            log.info("获取优惠金额：{}", discount);
        }

        BigDecimal payPrice = order.getPayPrice();
        if (discount != null) {
            BigDecimal price = order.getPayPrice().subtract(discount);
            payPrice = price.compareTo(BigDecimal.ZERO) < 0
                    ? BigDecimal.valueOf(0.01)
                    : price;
        }
        log.info("优惠前：{}, 优惠后：{}", order.getPayPrice(), payPrice);

        // 余额支付 TODO 扣款可提现金额，或者师傅的话，
//        if (orderPayDto.getType() == 0) {
//            Double balance = userListMapper.selectBalance(userId);
//            if (BigDecimal.valueOf(balance).compareTo(payPrice) < 0) {
//                return ResponseObject.fail("余额不足");
//            }
//            // 调用业务方法完成支付并回调
//            orderResult(order);
//            return ResponseObject.ok(true);
//        }

        //微信小程序支付
        if (orderPayDto.getType() == 1) {
            ImsUser userList = userListMapper.selectByPrimaryKey(userId);
//            ImsCouponRecord rec = couponRecordMapper.selectByUserIdAndCouponId(userId, orderPayDto.getCouponId());
            if ("0".equals(isApp)) {
                WxAppPayReq req = new WxAppPayReq();
                req.setOpenId(userList.getWxOpenid());
                req.setOrderCode(order.getOrderCode());
                req.setPayPrice(payPrice);
                req.setAttach("Massage-" + orderPayDto.getCouponId());

                try {
                    UnifiedOrderRes unifiedOrderRes = payService.wxAppPay(req, request.getRemoteAddr());
                    OrderPayVo vo = new OrderPayVo();
                    vo.setAppId(unifiedOrderRes.getAppid());
                    long timestamp = System.currentTimeMillis() / 1000;
                    vo.setTimestamp(timestamp);
                    String nonceStr = payService.genNonceStr();
                    vo.setNonceStr(nonceStr);
                    vo.setPrepayId(unifiedOrderRes.getPrepayId());
                    vo.setPackageValue("Sign=WXPay");
                    vo.setPartnerId(mchId);

                    //前端需要的sign仅需这几个  坑
                    SortedMap<String, String> signParams = new TreeMap<>();
                    signParams.put("appId", unifiedOrderRes.getAppid());
                    signParams.put("timeStamp", String.valueOf(timestamp));
                    signParams.put("nonceStr", nonceStr);
                    signParams.put("package", "prepay_id=" + unifiedOrderRes.getPrepayId());
                    signParams.put("signType", "MD5");
                    String paySign = payService.buildSign(signParams);

                    vo.setSign(paySign);
                    return ResponseObject.ok(vo);
                } catch (Exception e) {
                    log.error("支付失败", e);
                    throw new RuntimeException(e);
                }
            } else if ("1".equals(isApp)) {
                WxAppPayReq req = new WxAppPayReq();
                req.setOrderCode(order.getOrderCode());
                req.setPayPrice(payPrice);
                req.setAttach("Massage-" + orderPayDto.getCouponId());
                // APP支付不需要 openId
                try {
                    // 1. 调用微信APP下单V3接口，获取prepay_id
                    UnifiedOrderRes unifiedOrderRes = payService.wxAppPayForApp(req, request.getRemoteAddr());
                    OrderPayVo vo = new OrderPayVo();
                    vo.setAppId(unifiedOrderRes.getAppid());
                    long timestamp = System.currentTimeMillis() / 1000;
                    vo.setTimestamp(timestamp);
                    String nonceStr = payService.genNonceStr();
                    vo.setNonceStr(nonceStr);
                    vo.setPrepayId(unifiedOrderRes.getPrepayId());
                    vo.setPackageValue("Sign=WXPay");
                    vo.setPartnerId(mchId);

                    //前端需要的sign仅需这几个  坑
                    SortedMap<String, String> signParams = new TreeMap<>();
                    signParams.put("appId", unifiedOrderRes.getAppid());
                    signParams.put("timeStamp", String.valueOf(timestamp));
                    signParams.put("nonceStr", nonceStr);
                    signParams.put("package", "prepay_id=" + unifiedOrderRes.getPrepayId());
                    signParams.put("signType", "MD5");
                    System.out.println("JSONObject.toJSONString(signParams) = " + JSONObject.toJSONString(signParams));
                    String paySign = payService.buildSign(signParams);
                    System.out.println("paySign = " + paySign);
                    vo.setSign(paySign);
                    return ResponseObject.ok(vo);
                } catch (Exception e) {
                    log.error("APP微信支付下单失败", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return ResponseObject.fail("支付失败");
    }




    @Override
    @Transactional
    public ResponseObject orderList(HttpServletRequest request, Integer isSpread, String name, Integer payType, int pageNum, int pageSize) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户：{}，订单列表入参：isSpread：{}， name：{}， payType：{}", userId, isSpread, name, payType);
        // 1. 超时自动取消订单
        autoCancelOrder(userId);

        PageHelper.startPage(pageNum, pageSize);
        //0查全部
        if (payType != null) {
            payType = payType == 0 ? null : payType;
        }
        //TODO用户界面查询师傅报价列表时，判断当前订单是否还存在有师傅报价，如果没有师傅报价，则不返回给前端
        List<ImsOrder> orders = orderListMapper.selectByCriteria(userId, isSpread, name, payType);
        List<OrderVo> orderVoList = new ArrayList<>();
        ServiceConfigVo schoolConfig = (ServiceConfigVo) cacheService.get("schoolConfig");
        Object cachedObject = cacheService.get("imsCoachLabelsMap");
        orders.forEach(order -> {
            if (order.getIsComment()==null){
                order.setIsComment((byte) 0);
            }
            OrderVo orderVo = new OrderVo();
            BeanUtil.copyProperties(order, orderVo);
            orderVo.setCreateTime(order.getCreateTime());
            orderVo.setCanRefund(Integer.valueOf(order.getPayType()));
            List<ImsOrderGoods> orderGoodsLists = orderGoodsListMapper.selectByUserIdAndOrderId(userId, order.getId());
            if (orderGoodsLists != null && !orderGoodsLists.isEmpty()) {
                orderVo.setGoodsId(orderGoodsLists.get(0).getGoodsId());
                orderVo.setGoodsName(orderGoodsLists.get(0).getGoodsName());
                orderVo.setGoodsCover(orderGoodsLists.get(0).getGoodsCover());
            }
            List<QuotedPriceVo> quotedPrices = quotedPriceMapper.selectByOrderId(order.getId());
            for (QuotedPriceVo quotedPrice : quotedPrices) {
                quotedPrice.setMobile(ConfidentialUtils.maskPhone(quotedPrice.getMobile()));
                if (quotedPrice.getLabelId() == null || quotedPrice.getLabelId() == 0) {
                    //todo  这里如果后台修改普通师傅id了  这里也要做变更
                    quotedPrice.setLabelId(25);
                }
                Integer lastCommission = CalculateProportionUtils.coachProportion(quotedPrice.getLabelId(),schoolConfig,cachedObject);
                //根据师傅不同等级进行报价金额展示
                // 计算比例：(100 + lastCommission) / 100，保留2位小数（四舍五入）
                BigDecimal lastProportion = new BigDecimal(100 + lastCommission)
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                log.info("报价回显计算·······当前师傅得到的订单比例为：{}",lastProportion);
                BigDecimal multiply = quotedPrice.getPrice().multiply(lastProportion).setScale(2, RoundingMode.HALF_UP);
                quotedPrice.setPrice(multiply);
            }
            List<ImsOrderDiffPriceVo> imsOrderDiffPrice = orderDiffPriceMapper.selectInfoByOrderId(order.getId());
            for (ImsOrderDiffPriceVo imsOrderDiffPriceVo : imsOrderDiffPrice) {
                imsOrderDiffPriceVo.setCreditPrice(BigDecimal.ZERO);
            }
            orderVo.setOrderDiffPrices(imsOrderDiffPrice);
            orderVo.setQuotedPriceVos(quotedPrices);
            orderVoList.add(orderVo);
        });

        PageResult<OrderVo> res = new PageResult<>(new PageInfo<>(orderVoList));
        return ResponseObject.ok(res);
    }



    @Override
    @Transactional
    public void coachBalanceArr(Integer userId) {
        // 查询支付完成且未到账的订单
        List<ImsOrder> orders = orderListMapper.findByPayTypeAndUserId(7, userId);
        for (ImsOrder order : orders) {
            // 如果存在未处理售后，不结算
            ImsRefundOrder refund = refundOrderMapper.findByOrderIdAndStatus(order.getId(), 1);
            if (refund != null) {
                continue;
            }
            // 1. 结算分销佣金
            successCash(order.getId());
            // 2. 更新订单 have_tx=1
//            order.setHaveTx((byte) 1);
            orderListMapper.updateByPrimaryKey(order);
            // 3. 给师傅加服务费和上门费
            BigDecimal coachCash = order.getCoachCash() != null ? order.getCoachCash() : BigDecimal.valueOf(0);
//            BigDecimal carCash = BigDecimal.valueOf(order.getTrueCarPrice() != null ? order.getTrueCarPrice() : 0);
            ImsCoach coachList = coachListMapper.selectByPrimaryKey(order.getCoachId());
            if (coachList != null) {
                coachList.setServicePrice(
                        coachCash.add(
                                coachList.getServicePrice() == null ? BigDecimal.ZERO : coachList.getServicePrice()
                        )
                );


//                coachList.setCarPrice(
//                        carCash.add(
//                                BigDecimal.valueOf(
//                                        coachList.getCarPrice() == null ? 0 : coachList.getCarPrice()
//                                )
//                        ).doubleValue()
//                );
                coachListMapper.updateByPrimaryKey(coachList);
            }
            // 4. 给代理商加佣金
            if (order.getAdminId() != null && order.getAdminCash() != null && order.getAdminCash().compareTo(BigDecimal.ZERO) > 0) {
                ImsShequshopSchoolAdmin schoolAdmin = schoolAdminMapper.selectByPrimaryKey(order.getAdminId());
                if (schoolAdmin != null) {
                    schoolAdmin.setCash(order.getAdminCash().add(
                            schoolAdmin.getCash() == null ? BigDecimal.ZERO : BigDecimal.valueOf(schoolAdmin.getCash())).doubleValue()
                    );
                }
                schoolAdminMapper.updateByPrimaryKey(schoolAdmin);
            }
        }
    }

    @Override
    public ResponseObject listPriceSetting(HttpServletRequest request, PriceSettingDto req) {
        Integer userId = (Integer) request.getAttribute("userId");
        List<ImsServicePriceSettingWithBLOBs> priceSettingList = priceSettingMapper.selectByServiceIdAndType(req.getId(), req.getType());
        List<ImsServicePriceSettingOrder> settingOrderList = priceSettingOrderMapper.selectByUserIdAndServiceIdAndOrderId(userId, req.getId(), 0, null);

        if (settingOrderList != null && !settingOrderList.isEmpty()) {
            priceSettingList.forEach(priceSetting -> {
                String val = priceSettingOrderMapper.selectValBySettingIdAndOrderIdAndUserId(priceSetting.getId(), 0, userId);
                priceSetting.setVal(val);
            });
        }

        return ResponseObject.ok(priceSettingList);
    }

    @Override
    public ResponseObject submitPriceSettings(HttpServletRequest request, PriceSettingSubmitDto req) {
        Integer userId = (Integer) request.getAttribute("userId");
        //防止顶掉，仅删除orderId为0的,0为预下单，还没下单，  防止顶掉别人的，仅删除自己的
        priceSettingOrderMapper.deleteByServiceId(req.getId(), userId, 0);
        // 2. 组装待插入列表
        log.info("{}报价/一口价提交{}", userId, JSONObject.toJSONString(req));
        List<ImsServicePriceSettingOrder> list = req.getData().stream().map(d -> {
            ImsServicePriceSettingOrder o = new ImsServicePriceSettingOrder();
            o.setSettingId(d.getSettingId());
            o.setServiceId(d.getServiceId());
            o.setUserId(userId);
            o.setVal(d.getVal());
            o.setCreateTime(new Date());
            return o;
        }).collect(Collectors.toList());
        if (!list.isEmpty()) {
            priceSettingOrderMapper.insertBatch(list);
        }
        return ResponseObject.ok();
    }

    @Override
    @Transactional
    public ResponseObject payOrderShifu(Integer userId, Integer isApp, PayOrderShifuDto req) {
        log.info("用户：{} 下单：{}", userId, JSONObject.toJSONString(req));

        //查询当前用户是否有上级分销员
        ImsUser userResult = userListMapper.selectByPrimaryKey(userId);
        if (userResult != null) {
            req.setPid(userResult.getPid());
            if (userResult.getStatus() == 2){
                return ResponseObject.fail("您已被平台拉黑，不可进行此操作，如有异议请联系平台客服");
            }
        }

        ImsAddress address = addressMapper.selectByPrimaryKey(req.getAddressId());
        if (address == null) {
            return ResponseObject.fail("请添加地址");
        }

        ResponseObject res = payOrderInfoShifu(userId, req.getServiceId(), req.getNum(), address.getLat(), address.getLng(), req.getCouponId(), req.getAddressId());
        log.info("payOrderInfoShifu :{}", JSONObject.toJSONString(res));
        if (!res.getCode().equals("200")) {
            return res;
        }
        CalcResult orderInfo = (CalcResult) res.getData();
        if (orderInfo.getOrderGoodsList() == null) {
                return ResponseObject.fail("请选择服务项目，请刷新重试");
        }
        ServiceConfigVo schoolConfig = (ServiceConfigVo) cacheService.get("schoolConfig");

        //0一口价模式1报价模式
        int payType = 1;
        BigDecimal coachServicePrice = BigDecimal.ZERO;
        if (req.getType() != null && req.getType() == 1) {
            payType = -3;
        } else {
            coachServicePrice = BigDecimalUtil.roundTo2(
                    BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(10))
                            .multiply(BigDecimal.valueOf(30))
                            .multiply(BigDecimal.valueOf(0.01))
                    );
        }
        //构建订单信息
        ImsOrder orderInsert = new ImsOrder();
        orderInsert.setOverTime(new Date(System.currentTimeMillis() + schoolConfig.getOverTime() * 60 * 1000));
        orderInsert.setOrderCode(generateOrderCode());
        orderInsert.setUserId(userId);
        orderInsert.setPayPrice(orderInfo.getPayPrice());
        orderInsert.setServicePrice(orderInfo.getGoodsPrice().doubleValue());
        orderInsert.setDiscount(orderInfo.getDiscount().doubleValue());
        orderInsert.setPayType((byte) payType);
        orderInsert.setType(req.getType());
        orderInsert.setStartTime(req.getStartTime());
        orderInsert.setEndTime(req.getEndTime());
        orderInsert.setDistance(0.00);
        orderInsert.setTimeLong(0);
        orderInsert.setTrueTimeLong(0);
        orderInsert.setText(req.getText());
        orderInsert.setChannelId(0);
        orderInsert.setUrgent(req.getUrgent() == null ? 0 : req.getUrgent());
        orderInsert.setAppPay(isApp);
        orderInsert.setCoachServicePrice(coachServicePrice);
        orderInsert.setIsShow((byte) 1);
        orderInsert.setCreateTime(new Date());
        orderInsert.setQuotationNum(0);
//        orderInsert.setPayTime(null);
        //退款状态0未申请（取消申请）1退款成功（同意退款）2退款申请中3拒绝退款
        orderInsert.setRefundStatus((byte) 0);
        //是否售后订单
        orderInsert.setIsAftermarket((byte) 0);
        //记录分销员id 上级用户
        if (req.getPid() != null && req.getPid() != 0) {
            ImsUser userList = userListMapper.selectByPrimaryKey(req.getPid());
            if (userList == null) {
                return ResponseObject.fail("分销员不存在");
            }
            orderInsert.setTopUid(req.getPid());
            //二级分销,已去除此功能
            orderInsert.setSecondUid(0);
        } else {
            orderInsert.setTopUid(0);
            orderInsert.setSecondUid(0);
        }
        //下单
        int i = orderListMapper.insert(orderInsert);
        if (i != 1) {
            return ResponseObject.fail("下单失败");
        }
        //往订单阅读状态表中插入数据
        ImsOrderReadStatus readStatus = new ImsOrderReadStatus();
        readStatus.setOrderId(orderInsert.getId());
        readStatus.setUserId(userId);
        readStatus.setRole(1);
        readStatus.setReadFlag(0);
        readStatus.setPayType(-3);
        readStatus.setCreateTime(DateTime.now());
        readStatusMapper.insertSelective(readStatus);
        log.info("插入用户消息表成功，当前用户为{}，订单号为{}",userId,orderInsert.getId());

        //使用优惠券
        Integer orderId = orderInsert.getId();
        if (req.getCouponId() != null && req.getCouponId() > 0) {
            Integer usedRecId = couponUse(req.getCouponId(), userId, orderId);
            orderListMapper.updateCouponAndDiscount(orderId, req.getCouponId(), BigDecimal.valueOf(orderInsert.getDiscount()));
        }

        //添加下单地址
        orderAddressAdd(req.getAddressId(), orderId);

        //更新市级代理的佣金
        ImsShequshopSchoolAdmin schoolAdmin = schoolAdminMapper.selectByCityIdAndTypeAndStatus(address.getCityId(), 2, 1);
        if (schoolAdmin != null) {
            orderListMapper.updateAdminIdById(orderId, schoolAdmin.getId());
        }

        if (req.getCarIds() != null && !req.getCarIds().isEmpty()) {
            for (Integer carId : req.getCarIds()) {
                ImsCar imsCar = imsCarMapper.selectByPrimaryKey(carId);
                orderGoodsAddShifu(orderInfo.getOrderGoodsList(), orderId, 0, userId, payType, orderInfo.getPayPrice(), imsCar.getNum(), carId);
            }
        } else {
            //添加到子订单
            orderGoodsAddShifu(orderInfo.getOrderGoodsList(), orderId, 0, userId, payType, orderInfo.getPayPrice(), req.getNum(), 0);
        }


        //setting_order匹配订单
//        List<ImsServicePriceSettingOrder> settingOrders = priceSettingOrderMapper.selectByUserIdAndServiceIdAndOrderId(userId, req.getServiceId(), 0);
        priceSettingOrderMapper.updateByUserIdAndServiceIdAndOrderId(userId, req.getServiceId(), orderId, req.getCarIds());

        //找出需要推送接单的师傅信息
        List<PushCoachInfo> coachInfoList = couponMapper.selectNearCoachList(address.getLat(), address.getLng(), schoolConfig.getDistance(), req.getServiceId());
        if (coachInfoList != null && !coachInfoList.isEmpty()) {
            log.info("订单{}下单完成，附近{}公里有{}位师傅", orderId, schoolConfig.getDistance(), coachInfoList.size());
//            //删除openId为空的
//            //后续还有其他地方会用到上面sql， 所以这里在代码里处理openid为空的
//            List<PushCoachInfo> toPush = coachInfoList.stream()
//                    .filter(info -> info.getOpenId() != null && !info.getOpenId().trim().isEmpty())
//                    .collect(Collectors.toList());
//            log.info("订单{}下单完成，去除未登录，需推送师傅：{}", orderId, JSONObject.toJSONString(toPush));

            if (!coachInfoList.isEmpty()) {
                //构建模板信息
                ImsUser userInfo = userListMapper.selectByPrimaryKey(userId);
                TemplateData data = new TemplateData()
                        .add("character_string1", orderInsert.getOrderCode())
                        .add("thing2", StringUtils.isEmpty(userInfo.getNickName()) ? ConfidentialUtils.maskPhone(userInfo.getPhone()) : ConfidentialUtils.maskPhone(userInfo.getNickName()))
                        .add("thing3", address.getAddress() == null ? "..." : (address.getAddress().length() > 16 ? address.getAddress().substring(0, 16) + "..." : address.getAddress()))
                        .add("phrase4", "待确认报价")
                        .add("time5", TimestampUtils.formatTimestamp(System.currentTimeMillis()));

                CompletableFuture.runAsync(() -> {
                    for (PushCoachInfo info : coachInfoList) {
                        try {
                            // 并发度控制
                            pushSemaphore.acquire();
                            try {
                                // 阻塞式取令牌
                                while (!rateLimiter.tryAcquire()) {
                                    // 睡 50ms 再重试，避免 CPU 忙等
                                    Thread.sleep(50);
                                }

                                // 拿到令牌后再推送
                                String resp = pushService.sendMessage(
                                        WxSubscribeTemplate.NEW_ORDER_FOR_MASTER.getTemplateId(),
                                        "/shifu/Receiving",
                                        userListMapper.selectByPrimaryKey(info.getUserId()),
                                        data,
                                        "zh_CN",
                                        (byte) 1,
                                        "您附近有一条新的服务订单"
                                );
                                log.info("订单：{} {}:{} ---> {}",
                                        orderId,
                                        WxSubscribeTemplate.NEW_ORDER_FOR_MASTER.getDescription(),
                                        info.getUserId(),
                                        resp
                                );
                            } finally {
                                pushSemaphore.release();
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.error("推送线程被中断 userId={}", info.getUserId(), e);
                        } catch (Exception e) {
                            log.error("推送失败 userId={}", info.getUserId(), e);
                        }
                    }
                }, pushExecutor);
            }
        }
        return ResponseObject.ok(orderId);
    }

    @Override
    @Transactional
    public ResponseObject cancelOrder(HttpServletRequest request, IdDto req) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户：{}，取消订单：{}", userId, req.getId());
        OrderInfoVo orderInfoVo = orderInfo(userId, req.getId(), false);
//        if (!"200".equals(responseObject.getCode())) {
//            return responseObject;
//        }
//        OrderInfoVo orderInfoVo = (OrderInfoVo) responseObject.getData();
//        ImsMassageServiceOrderList orderList = orderListMapper.selectByIdAndUserId(req.getId(), userId);

        if (orderInfoVo.getPayType() > 1) {
            return ResponseObject.fail("该订单状态错误");
        }

        int updated = orderListMapper.updatePayType(req.getId(), -1);
        if (updated != 1) {
            // 回滚后给前端明确错误
            if (updated == 0) {
                return ResponseObject.fail("已取消过了，不能再次取消");
            }
            return ResponseObject.fail("取消失败");
        }

        //退换库存
        for (ImsOrderGoods orderGood : orderInfoVo.getOrderGoods()) {
            setOrDelStock(orderGood.getGoodsId(), orderGood.getNum(), 1);
        }

        //退换优惠券
        if (orderInfoVo.getCouponId() != null) {
            ImsCouponRecord couponRecord = couponRecordMapper.selectByPrimaryKey(orderInfoVo.getCouponId());
            if (couponRecord != null) {
                ImsCouponRecord update = new ImsCouponRecord();
                update.setId(orderInfoVo.getCouponId());
                update.setStatus((byte) 1);
                update.setUseTime(null);
                update.setOrderId(0);
                couponRecordMapper.updateByPrimaryKeySelective(couponRecord);
            }
        }
        return ResponseObject.ok(orderInfoVo.getId());
    }

    @Transactional
    public void orderGoodsAddShifu(ImsServiceList orderGoods, Integer orderId, int capId, Integer userId, int payType, BigDecimal payPrice, Integer num, int carId) {
        ImsOrderGoods orderGoodsList = new ImsOrderGoods();
        orderGoodsList.setOrderId(orderId);
        orderGoodsList.setUserId(userId);
        orderGoodsList.setPayType((byte) payType);
        orderGoodsList.setCoachId(capId);
        orderGoodsList.setGoodsName(orderGoods.getTitle());
        orderGoodsList.setGoodsCover(orderGoods.getCover());
        orderGoodsList.setPrice(orderGoods.getPrice());
        orderGoodsList.setTruePrice(payPrice.doubleValue());
        orderGoodsList.setNum(num);
        orderGoodsList.setCanRefundNum(num);
        orderGoodsList.setGoodsId(orderGoods.getId());
        orderGoodsList.setTimeLong(orderGoods.getTimeLong());
        orderGoodsList.setSettingOrderId(0);
        orderGoodsList.setCarId(carId);
        orderGoodsListMapper.insert(orderGoodsList);
        //减少库存 增加销量
        setOrDelStock(orderGoods.getId(), num, 2);
    }

    @Transactional
    public void orderAddressAdd(Integer addressId, Integer orderId) {
        ImsAddress address = addressMapper.selectByPrimaryKey(addressId);
        if (address == null) {
            throw new RuntimeException("下单地址被删除了");
        }
        ImsOrderAddress orderAddress = new ImsOrderAddress();
//        BeanUtil.copyProperties(address, orderAddress);
        orderAddress.setUserName(address.getUserName());
        orderAddress.setMobile(address.getMobile());
        orderAddress.setProvinceId(address.getProvinceId());
        orderAddress.setCityId(address.getCityId());
        orderAddress.setAreaId(address.getAreaId());
        orderAddress.setLng(address.getLng());
        orderAddress.setLat(address.getLat());
        orderAddress.setAddress(address.getAddress());
        orderAddress.setHouseNumber(address.getHouseNumber());
        orderAddress.setAddressInfo(address.getAddressInfo());

        orderAddress.setOrderId(orderId);
        orderAddressMapper.insert(orderAddress);
    }


    public static String generateOrderCode() {
        // 1. 随机数 1–999
        int rand = ThreadLocalRandom.current().nextInt(1, 1000);
        String randStr = String.valueOf(rand);

        // 2. 当前时间，格式 yyyyMMddHHmmss
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss")
                .format(new Date());

        // 3. 前半段：时间 + "0" + rand + "0"
        StringBuilder code = new StringBuilder(timestamp)
                .append('0')
                .append(randStr)
                .append('0');

        // 4. 补足零：总长度要在这里再拼一个 rand 前填充 (7 - randStr.length()) 个 '0'
        int zeroCount = 7 - randStr.length();
        for (int i = 0; i < zeroCount; i++) {
            code.append('0');
        }

        // 5. 拼上第二次的 rand
        code.append(randStr);

        return code.toString();
    }


    private ResponseObject payOrderInfoShifu(Integer userId, Integer serviceId, Integer num, String lat, String lng, Integer couponId, Integer addressId) {
        ImsAddress serviceAddress = addressMapper.selectByPrimaryKey(addressId);
        Integer cityId = serviceAddress.getCityId();
        ImsServiceList service = serviceListMapper.selectByIdAndCityId(serviceId, cityId);
        if (service == null || service.getStatus() != 1) {
            return ResponseObject.fail("该服务不存在或当前城市不可用");
        }

        CalcResult res = new CalcResult();
        res.setCouponId(couponId);
        res.setOrderGoodsList(service);
        //实付金额，TODO 这里是因为没报价么？金额全是 0
        BigDecimal payPrice = BigDecimal.ZERO;
        if (couponId != null && couponId > 0) {
            ResponseObject cd = getCouponDiscount(couponId, BigDecimal.valueOf(service.getPrice()).multiply(BigDecimal.valueOf(num)), userId);
            log.info("优惠卷信息：{}", cd);
            if (!"200".equals(cd.getCode())) {
                return cd;
            }
            // cd.data = Map.of("discount", BigDecimal)
            //折扣金额
            BigDecimal discount = BigDecimal.valueOf((Double) cd.getData());
            if (discount != null) {
                res.setDiscount(discount);
                BigDecimal price = BigDecimal.valueOf(service.getPrice()).multiply(BigDecimal.valueOf(num)).subtract(discount);
                if (price.compareTo(BigDecimal.ZERO) <= 0) {
                    payPrice = BigDecimal.ZERO;
                } else {
                    payPrice = price;
                }
            } else {
                res.setDiscount(BigDecimal.ZERO);
            }
        } else {
            payPrice = BigDecimal.valueOf(service.getPrice()).multiply(BigDecimal.valueOf(num));
            res.setDiscount(BigDecimal.ZERO);
        }
        res.setPayPrice(BigDecimalUtil.roundTo2(payPrice));
        res.setGoodsPrice(BigDecimalUtil.roundTo2(BigDecimal.valueOf(service.getPrice())));
        res.setInitGoodsPrice(BigDecimalUtil.roundTo2(BigDecimal.valueOf(service.getPrice()).multiply(BigDecimal.valueOf(num))));
        return ResponseObject.ok(res);
    }

    //佣金到账
    private void successCash(Integer orderId) {
        ImsOrderCommission commission = orderCommissionMapper.selectByOrderIdAndType(orderId, 1);
        if (commission != null && commission.getStatus() == 1) {
            ImsUser userList = userListMapper.selectByPrimaryKey(commission.getTopId());
            assert userList != null;
            userList.setNewCash(userList.getNewCash().add(BigDecimal.valueOf(commission.getCash())));
            userList.setCash(BigDecimal.valueOf(userList.getCash()).add(BigDecimal.valueOf(commission.getCash())).doubleValue());
            userListMapper.updateByPrimaryKeySelective(userList);
            commission.setStatus(2);
            commission.setCashTime(new Date());
            orderCommissionMapper.updateByPrimaryKey(commission);
        }
    }

    //    超时自动取消订单
    @Transactional
    public void autoCancelOrder(Integer userId) {
        List<ImsOrder> toCancel = orderListMapper.selectTimeoutOrders(userId, Instant.now().getEpochSecond());
        for (ImsOrder o : toCancel) {
            cancelOrder(o, userId);
        }
    }

    @Transactional
    public void cancelOrder(ImsOrder order, Integer userId) {
        // 标记取消
        orderListMapper.updatePayType(order.getId(), -1);
        // 退库存
        List<ImsOrderGoods> goods = orderGoodsListMapper.selectByOrderId(order.getId());
        goods.forEach(g -> setOrDelStock(g.getGoodsId(), g.getNum(), 1));
        // 退优惠券-恢复默认值
        if (order.getCouponId() != null && order.getCouponId() > 0) {
            couponRecordMapper.updateStatusUse(userId, order.getCouponId(), 1, 0, 0);
        }
    }


    @Transactional
    public void setOrDelStock(Integer goodsId, Integer num, Integer type) {
        if (goodsId == null) {
            return;
        }

        ImsServiceList serviceList = serviceListMapper.selectByPrimaryKey(goodsId);
        if (type == 1) {
            //退款要退回库存＋减销量
            serviceList.setTrueSale(serviceList.getTrueSale() - num);
            serviceList.setTotalSale(serviceList.getTotalSale() - num);
            serviceList.setLock(serviceList.getLock() + 1);
            serviceListMapper.updateByPrimaryKeySelective(serviceList);
        } else if (type == 2) {
            //正常下单要减库存＋加销量
            serviceList.setTrueSale(serviceList.getTrueSale() + num);
            serviceList.setTotalSale(serviceList.getTotalSale() + num);
            serviceList.setLock(serviceList.getLock() + 1);
            serviceListMapper.updateByPrimaryKeySelective(serviceList);
        }
    }


    /**
     * 获取折扣金额
     **/
    public ResponseObject getCouponDiscount(Integer couponId, BigDecimal payPrice, Integer userId) {
        ImsCouponRecord rec = couponRecordMapper.selectByUserIdAndCouponId(userId, couponId);
        if (rec == null || !userId.equals(rec.getUserId())) {
            return ResponseObject.fail("你未领用此优惠券");
        }

        ImsCoupon info = couponMapper.selectByPrimaryKey(rec.getCouponId());
        if (info == null) {
            return ResponseObject.fail("优惠卷不存在");
        }
        if (info.getStatus() != 1) {
            return ResponseObject.fail("优惠券不能使用了");
        }
        long now = System.currentTimeMillis();
        if (info.getTimeLimit() == 0) {
            if (now > info.getEndTime().getTime()) return ResponseObject.fail("优惠券过期了");
            if (now < info.getStartTime().getTime()) return ResponseObject.fail("优惠券还未到使用时间");
        }
        if (info.getTimeLimit() == 1 &&
                now - rec.getCreateTime().getTime() > info.getDay() * 86400_000L) {
            return ResponseObject.fail("优惠券过期了");
        }
        if (info.getType() == 0 && info.getFull().compareTo(payPrice.doubleValue()) > 0) {
            return ResponseObject.fail("优惠券未满足支付金额的使用条件");
        }
        return ResponseObject.ok(info.getDiscount());
    }

    /**
     * 使用优惠券
     **/
    @Transactional
    public Integer couponUse(Integer couponId, Integer userId, Integer orderId) {
        ImsCouponRecord rec = couponRecordMapper.selectByUserIdAndCouponId(userId, couponId);
        if (rec != null && rec.getNum() > 1) {
            couponRecordMapper.updateNum(couponId, rec.getNum() - 1);
            ImsCouponRecord record = new ImsCouponRecord();
            BeanUtil.copyProperties(rec, record);
            record.setPid(couponId);
            record.setNum(1);
            record.setStatus((byte) 2);
            record.setUseTime(new Date());
            record.setOrderId(orderId);
            couponRecordMapper.insert(rec);
        } else {
            couponRecordMapper.updateStatusUse( userId,  couponId, 2, System.currentTimeMillis() / 1000, orderId);
        }
        ImsCoupon coupon = couponMapper.selectByPrimaryKey(couponId);
        if (coupon != null) {
            coupon.setHaveSend(coupon.getHaveSend() == null ? 1 : coupon.getHaveSend() + 1); //已使用
            couponMapper.updateByPrimaryKeySelective(coupon);
        }
        return couponId;
    }


    /**
     * 微信支付回调
     * @param orderInfo 订单信息
     * @param transactionId  支付id
     * @return 落库是否成功
     */
    @Override
    @Transactional
    public boolean orderResult(ImsOrder orderInfo, int couponId, String transactionId) {
        if (orderInfo == null) {
            log.info("微信支付回调 ---> 订单{}不存在", transactionId);
            return false;
        }
        log.info("微信支付回调 ---> {}订单状态：{}", orderInfo.getOrderCode(), orderInfo.getPayType());
        ImsOrder update = new ImsOrder();
        update.setId(orderInfo.getId());
        //0一口价模式1报价模式
        if (orderInfo.getType() == 1) {
            if (orderInfo.getPayType() < 2 && orderInfo.getPayType() != -1) {
                update.setPayType((byte) 3);
            }
        } else if (orderInfo.getType() == 0) {
            update.setPayType((byte) 2);
        }
        ImsCoupon imsMassageServiceCoupon = couponMapper.selectByPrimaryKey(couponId);
        update.setTransactionId(transactionId);
        update.setCouponId(couponId);
        if (imsMassageServiceCoupon != null && imsMassageServiceCoupon.getDiscount() != null) {
            update.setDiscount(imsMassageServiceCoupon.getDiscount());
        }
        update.setPayTime(new Date());
        int i = orderListMapper.updateByPrimaryKeySelective(update);
        //使用优惠卷
        couponUse(couponId, orderInfo.getUserId(), orderInfo.getId());
        //往订单阅读状态表中插入数据 用户的
        ImsOrderReadStatus readStatusUser = new ImsOrderReadStatus();
        readStatusUser.setOrderId(orderInfo.getId());
        readStatusUser.setUserId(orderInfo.getUserId());
        readStatusUser.setRole(1);
        readStatusUser.setPayType(5);
        readStatusUser.setReadFlag(0);
        readStatusUser.setCreateTime(DateTime.now());
        readStatusMapper.insertSelective(readStatusUser);

        //师傅的,一口价的先付钱，在发单
        if (orderInfo.getType() == 1 && orderInfo.getCoachId() != null) {
            ImsOrderReadStatus readStatusCoach = new ImsOrderReadStatus();
            readStatusCoach.setOrderId(orderInfo.getId());
            readStatusCoach.setUserId(orderInfo.getCoachId());
            readStatusCoach.setRole(2);
            readStatusCoach.setPayType(3);
            readStatusCoach.setReadFlag(0);
            readStatusCoach.setCreateTime(DateTime.now());
            readStatusMapper.insertSelective(readStatusCoach);
        }

        return i == 1;
    }

    @Override
    public ImsOrder getOrderInfo(String orderCode) {
        return orderListMapper.selectByOrderCode(orderCode);
    }

    @Override
    public String getGoodsName(Integer userId, Integer id) {
        List<ImsOrderGoods> orderGoodsList = orderGoodsListMapper.selectByUserIdAndOrderId(userId, id);
        if (orderGoodsList != null && orderGoodsList.size() > 0) {
            return orderGoodsList.get(0).getGoodsName();
        }
        return "";
    }


    /** 余额支付支付回调 **/
    @Transactional
    public boolean orderResult(ImsOrder order) {
        OrderInfoVo orderVo = dataInfo(order);

//        if (order.getPayType() < 2 && order.getPayType() != -1) {
//            Byte payType = (byte) (order.getType() == 1 ? 3 : 2);
//            order.setTransactionId(order.getOrderCode());
//            orderListMapper.updatePayResult(order.getId(), transactionId, payType, System.currentTimeMillis()/1000);
//        }
//          TODO 暂无余额支付，后期添加
//        // 1. 更新订单状态
//        // 2. 余额流水
//        // 3. 事务提交前可插入其他逻辑...
//        // 4. 提交事务
//        // 5. 添加后台提醒
//        // 6. 极光推送（省、市、区）略...

        return true;
    }

    public OrderInfoVo dataInfo(ImsOrder order) {
        OrderInfoVo orderVo = new OrderInfoVo();
        BeanUtil.copyProperties(order, orderVo);
//        List<ImsServicePriceSetting> priceSetting = priceSettingMapper.selectByOrderId(order.getId());
//        orderVo.setPriceSetting(priceSetting);
        //师傅信息
        if (order.getCoachId() != null) {
            ImsCoach coachInfo = coachListMapper.selectByPrimaryKey(order.getCoachId());
            orderVo.setCoachInfo(coachInfo);
        }
        //优惠卷信息
        if (order.getCouponId() != null) {
            ImsCouponRecord couponInfo = couponRecordMapper.selectByPrimaryKey(order.getCouponId());
            orderVo.setCouponInfo(couponInfo);
        }
        //售后服务信息
        ImsOrderAftermarket aftermarketInfo = orderAftermarketMapper.selectByOrderId(order.getId());
        orderVo.setAftermarketInfo(aftermarketInfo);
        return orderVo;
    }

    /**
     * 判断订单是否可退款
     *
     * @param payType      支付类型
     * @return true 可退款，false 不可退款
     */
    public static Integer canRefund(int payType) {
        List<Integer> eligible = Arrays.asList(-3, -2, 2, 3, 4, 5);
        return eligible.contains(payType) ? 1 : 0;
    }

    /**
     * 申请退款
     *
     * @param request
     * @param orderRefundDto
     * @return
     */
    @Transactional
    @Override
    public ResponseObject refundOrder(HttpServletRequest request, OrderRefundDto orderRefundDto) {
        if (orderRefundDto != null && orderRefundDto.getOrderId() != null) {
            Integer userId = (Integer) request.getAttribute("userId");
            log.info("用户：{}", userId);
            if (userId == null) {
                return ResponseObject.fail("您当前未登录或者无权操作");
            }
            //查询是否存在当前订单
            ImsOrder orderInfo = orderListMapper.selectByPrimaryKey(orderRefundDto.getOrderId());
            if (orderInfo == null) {
                return ResponseObject.fail("订单未找到");
            }
            ImsRefundOrder refundOrderRes = refundOrderMapper.selectByOrderId(orderRefundDto.getOrderId());
            if (refundOrderRes != null) {
                return ResponseObject.ok("当前订单已有申请退款记录，不可再次申请");
            }
            //查询订单子表中该订单数据
            List<ImsOrderGoods> imsOrderGoods = orderGoodsListMapper.selectByOrderId(orderRefundDto.getOrderId());
            if (imsOrderGoods == null || imsOrderGoods.isEmpty()) {
                return ResponseObject.fail("该订单子订单未找到");
            }
            ImsOrderGoods orderGoodInfo = imsOrderGoods.get(0);
            if (!userId.equals(orderInfo.getUserId())) {
                return ResponseObject.fail("此订单不属于您");
            }
            if (orderInfo.getPayType() < 2) {
                return ResponseObject.fail("此订单未支付");
            }
            if (orderInfo.getRefundStatus() == 1) {
                return ResponseObject.fail("此订单已退款不能重复提交");
            }
            if (orderInfo.getRefundStatus() == 2) {
                return ResponseObject.fail("此订单退款正在审核中");
            }
            if (orderInfo.getRefundStatus() == 3) {
                return ResponseObject.fail("您的退款申请已被驳回，请联系客服");
            }
            //pay_type 【 5待服务（已上门）】【6服务中（开始服务）】7已完成（师傅端3师傅接单待上门）
            if (orderInfo.getPayType() > 6) {
                return ResponseObject.fail("此订单状态无法申请退款");
            }
            int i = 0;
            try {
                //将订单数据插入到refund_order、refund_order_goods中，同时修改订单表中申请退款状态为2
                //入库refund_order
                ImsRefundOrder refundOrder = new ImsRefundOrder();
                //判断用户该订单是否使用优惠券，如果使用优惠券，则实际退款金额应为实际支付金额
                if (orderInfo.getCouponId() != null
                        && orderInfo.getDiscount() != null
                        && orderInfo.getCouponId() != 0
                        && orderInfo.getDiscount() != 0) {
                    BigDecimal payPrice = orderInfo.getPayPrice()
                            .subtract(BigDecimal.valueOf(orderInfo.getDiscount()))
                            .setScale(2, RoundingMode.HALF_UP);
                    // 如果结果为 0 或负数，则设置为 0.01
                    if (payPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        payPrice = new BigDecimal("0.01");
                    }
                    orderInfo.setPayPrice(payPrice);
                }
                refundOrder.setOrderCode(OrderCodeGenerator.generateOrderCode());
                refundOrder.setOrderId(orderInfo.getId());
                refundOrder.setUserId(orderInfo.getUserId());
                refundOrder.setCoachId(orderInfo.getCoachId());
                refundOrder.setPayPrice(BigDecimal.ZERO);
                refundOrder.setApplyPrice(orderInfo.getPayPrice());
                refundOrder.setRefundPrice(BigDecimal.ZERO);
                refundOrder.setStatus(1);
                refundOrder.setText(orderRefundDto.getText());
                refundOrder.setCreateTime(new Date());
                refundOrder.setServicePrice(orderInfo.getPayPrice());
                refundOrder.setCarPrice(orderInfo.getCarPrice() == null ? BigDecimal.ZERO : new BigDecimal(orderInfo.getCarPrice()));
                refundOrder.setAdminId(orderInfo.getAdminId() == null ? 0 : orderInfo.getAdminId());
                i = refundOrderMapper.insertSelective(refundOrder);
                if (i == 0) {
                    return ResponseObject.fail("申请失败");
                }
                //入库refund_order_goods
                ImsRefundOrderGoods refundOrderGoods = new ImsRefundOrderGoods();
                refundOrderGoods.setRefundId(refundOrder.getId());
                refundOrderGoods.setGoodsId(orderGoodInfo.getGoodsId());
                refundOrderGoods.setGoodsName(orderGoodInfo.getGoodsName());
                refundOrderGoods.setGoodsCover(orderGoodInfo.getGoodsCover());
                refundOrderGoods.setGoodsPrice(new BigDecimal(orderGoodInfo.getPrice()));
                refundOrderGoods.setNum(orderGoodInfo.getNum());
                refundOrderGoods.setOrderGoodsId(orderGoodInfo.getId());
                refundOrderGoods.setOrderId(orderGoodInfo.getOrderId());
                refundOrderGoods.setStatus(1);
                i = refundOrderGoodsMapper.insertSelective(refundOrderGoods);

                //修改订单表中申请退款状态为2
                ImsOrder orderList = new ImsOrder();
                orderList.setId(orderInfo.getId());
                orderList.setRefundStatus((byte) 2);
                i = orderListMapper.updateByPrimaryKeySelective(orderList);

                List<ImsOrderDiffPrice> imsOrderDiffPrices = orderDiffPriceMapper.selectByOrderId(orderInfo.getId());
                if (imsOrderDiffPrices != null && !imsOrderDiffPrices.isEmpty()){
                    for (ImsOrderDiffPrice imsOrderDiffPrice : imsOrderDiffPrices) {
                        //根据加价次数进行添加退款申请记录
                        ImsOrderDiffPriceRefund diffPriceRefund = new ImsOrderDiffPriceRefund();
                        diffPriceRefund.setDiffOrderId(imsOrderDiffPrice.getId());
                        diffPriceRefund.setOriginalOrderId(imsOrderDiffPrice.getOrderId());
                        diffPriceRefund.setApplyAmount(imsOrderDiffPrice.getApplyPrice());
                        diffPriceRefund.setStatus((byte) 1);
                        diffPriceRefund.setUserId(userId);
                        diffPriceRefund.setCoachId(diffPriceRefund.getCoachId());
                        diffPriceRefund.setRefundReason(orderRefundDto.getText());
                        diffPriceRefund.setRefundNo(OrderCodeGenerator.generateOrderCode());
                        diffPriceRefund.setCreateTime(new Date());
                        diffPriceRefundMapper.insertSelective(diffPriceRefund);

                        //修改原差价订单退款状态
                        imsOrderDiffPrice.setRefundType((byte) 2);
                        orderDiffPriceMapper.updateByPrimaryKeySelective(imsOrderDiffPrice);
                    }
                }
                //TODO这里应该推送订单师傅，订单用户申请退款，让师傅行动前和平台确认订单是否已取消

            }catch (Exception e) {
                return ResponseObject.fail("申请失败，请联系管理员");
            }
            return i == 0 ? ResponseObject.fail("申请失败") : ResponseObject.ok("申请成功，等待管理审核");
        }
        return ResponseObject.fail("申请失败，参数传入有误");
    }

    @Override
    public ResponseObject readStatus(HttpServletRequest request, ReadStatusDto readStatusDto) {
        if (readStatusDto == null) {
            return ResponseObject.fail("浏览阅读数量参数传入有误");
        }
        List<ImsOrderReadStatus> orderReadStatusList = readStatusMapper.selectReadStatus(readStatusDto);
        if (orderReadStatusList.size() == 0) {
            return ResponseObject.ok(null);
        }
        //定义不同值来存储各类消息总数
        int orderCount = 0;
        int shiFuBaoJia = 0;
        int daiZhiFu = 0;
        int daiFuWu = 0;
        int yiWanCheng = 0;
        int daiShangMen = 0;
        int fuWuZhong = 0;
        int shouHou = 0;
        //新建map来存储返回给前端
        Map<String,Integer> orderReadStatusMap = new HashMap<>();
        HashSet<Integer> orderIdSet = new HashSet<>();
        if (readStatusDto.getRole() == 1){
            for (ImsOrderReadStatus readStatus : orderReadStatusList) {
                //根据不同类型返回前端[]
                if(readStatus.getPayType()==1){
                    ++daiZhiFu;
                }
                if(readStatus.getPayType()==-2){
                    ++shiFuBaoJia;
                }
                if(readStatus.getPayType()==5){
                    ++daiFuWu;
                }
                if(readStatus.getPayType()==6){
                    ++fuWuZhong;
                }
                if(readStatus.getPayType()==7){
                    ++yiWanCheng;
                }
                if (!orderIdSet.contains(readStatus.getOrderId())) {
                    orderIdSet.add(readStatus.getOrderId());
                    orderCount++;
                }
            }
            orderReadStatusMap.put("shiFuBaoJia",shiFuBaoJia);
            orderReadStatusMap.put("daiZhiFu",daiZhiFu);
            orderReadStatusMap.put("daiFuWu",daiFuWu);
            orderReadStatusMap.put("fuWuZhong",fuWuZhong);
            orderReadStatusMap.put("yiWanCheng",yiWanCheng);
            orderReadStatusMap.put("countOrder",orderCount);
            return ResponseObject.ok(orderReadStatusMap);
        }else {
            for (ImsOrderReadStatus readStatus : orderReadStatusList) {
                if (readStatus.getPayType() == 1) {
                    ++daiZhiFu;
                }
                if (readStatus.getPayType() == 3) {
                    ++daiShangMen;
                }
                if (readStatus.getPayType() == 5) {
                    ++daiFuWu;
                }
                if (readStatus.getPayType() == 6) {
                    ++fuWuZhong;
                }
                if (readStatus.getPayType() == 7) {
                    ++yiWanCheng;
                }
                if (readStatus.getPayType() == 8) {
                    ++shouHou;
                }
                if (!orderIdSet.contains(readStatus.getOrderId())) {
                    orderIdSet.add(readStatus.getOrderId());
                    orderCount++;
                }
            }
            orderReadStatusMap.put("daiZhiFu",daiZhiFu);
            orderReadStatusMap.put("daiShangMen",daiShangMen);
            orderReadStatusMap.put("daiFuWu",daiFuWu);
            orderReadStatusMap.put("fuWuZhong",fuWuZhong);
            orderReadStatusMap.put("yiWanCheng",yiWanCheng);
            orderReadStatusMap.put("shouHou",shouHou);
            orderReadStatusMap.put("countOrder",orderCount);
            return ResponseObject.ok(orderReadStatusMap);
        }
    }

    @Override
    public ResponseObject updateReadStatus(HttpServletRequest request, ReadStatusDto readStatusDto) {
        if (readStatusDto == null) {
            return ResponseObject.fail("浏览阅读数量参数传入有误");
        }
        List<ImsOrderReadStatus> orderReadStatuses = readStatusMapper.selectReadStatus(readStatusDto);
        //判断该用户或者师傅是否有未读信息，如果无则直接返回
        if (orderReadStatuses.size() == 0) {
            return ResponseObject.ok();
        }
        readStatusMapper.updateByRole(readStatusDto);
        return ResponseObject.ok();
    }

    @Override
    @Transactional
    public ResponseObject aftermarket(HttpServletRequest request, AftermarketDto aftermarketDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户：{}", userId);
        if (userId == null) {
            return ResponseObject.fail("您当前未登录或者无权操作");
        }
        //查询是否存在当前订单
        ImsOrder orderInfo = orderListMapper.selectByPrimaryKey(aftermarketDto.getOrderId());
        if (orderInfo == null) {
            return ResponseObject.fail("订单未找到");
        }
        if (orderInfo.getPayType() != 7) {
            return ResponseObject.fail("订单未完成无法申请售后");
        }
        if (!userId.equals(orderInfo.getUserId())) {
            return ResponseObject.fail("您没有操作此订单的权限");
        }
        if (orderInfo.getIsAftermarket() == 1) {
            return ResponseObject.fail("此订单已经申请售后");
        }
        int i = 0;
        //添加数据到售后表
        ImsOrderAftermarket aftermarket = new ImsOrderAftermarket();
        aftermarket.setUserId(userId);
        aftermarket.setOrderId(orderInfo.getId());
        aftermarket.setOrderCode(orderInfo.getOrderCode());
        aftermarket.setRemark(aftermarketDto.getRemark());
        aftermarket.setStatus((byte) 0);
        aftermarket.setAdminId(orderInfo.getAdminId());
        aftermarket.setCreateTime(new Date());
        aftermarket.setUpdateTime(new Date());
        i = aftermarketMapper.insertSelective(aftermarket);

        //修改订单表中售后状态为1
        ImsOrder orderList = new ImsOrder();
        orderList.setId(orderInfo.getId());
        orderList.setIsAftermarket((byte) 1);
        i = orderListMapper.updateByPrimaryKeySelective(orderList);

        //用户申请售后通知师傅有售后消息
        ImsOrderReadStatus readStatusUser = new ImsOrderReadStatus();
        readStatusUser.setOrderId(orderInfo.getId());
        readStatusUser.setUserId(orderInfo.getCoachId());
        readStatusUser.setRole(2);
        readStatusUser.setPayType(8);
        readStatusUser.setReadFlag(0);
        readStatusUser.setCreateTime(DateTime.now());
        readStatusMapper.insertSelective(readStatusUser);
        return i == 0 ? ResponseObject.fail("售后提交失败") : ResponseObject.ok("售后提交成功");
    }

    @Override
    @Transactional
    public ResponseObject addComment(HttpServletRequest request, OrderCommentDto commentDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户：{}", userId);
        if(userId == null) {
            return ResponseObject.fail("无效用户，请登录");
        }
        //判断此订单是否属于当前登录用户
        ImsOrder order = orderListMapper.selectByPrimaryKey(commentDto.getOrderId());
        if (order==null){
            return ResponseObject.fail("查无此订单");
        }
        if(!userId.equals(order.getUserId())){
            return ResponseObject.fail("该订单不属于你，无权评价");
        }
        if(order.getPayType()!=7){
            return ResponseObject.fail("订单未完成不能评价");
        }
        if ((order.getIsComment()== null ? 0 : order.getIsComment()) == 1){
            return ResponseObject.fail("你已评价过");
        }
        //落库order_comment
        ImsOrderComment orderComment = new ImsOrderComment();
        orderComment.setUserId(userId);
        orderComment.setOrderId(commentDto.getOrderId());
        orderComment.setStar(commentDto.getStar());
        orderComment.setText(commentDto.getText());
        orderComment.setCreateTime(new Date());
        orderComment.setStatus(1);
        orderComment.setCoachId(order.getCoachId());
        orderComment.setAdminId(order.getAdminId());
        orderComment.setImgs(commentDto.getImgs());
        //插入之前先查询是否存在该条评论
        ImsOrderComment orderCommentResult =  orderCommentMapper.selectByOrderId(order.getId());
        int i = 0;
        if (orderCommentResult == null) {
            i = orderCommentMapper.insertSelective(orderComment);
        } else {
            orderComment.setId(orderCommentResult.getId());
            i = orderCommentMapper.updateByPrimaryKeySelective(orderComment);
        }
        //落库order_comment_goods
        ImsOrderCommentGoods orderCommentGoods = new ImsOrderCommentGoods();
        orderCommentGoods.setServiceId(commentDto.getServiceId());
        orderCommentGoods.setCommentId(orderComment.getId());
        orderCommentGoods.setStar(BigDecimal.valueOf(commentDto.getStar()));
        ImsOrderCommentGoods orderCommentGoodsResult = commentGoodsMapper.selectByCommentId(orderComment.getId());
        if(orderCommentGoodsResult == null){
            i = commentGoodsMapper.insertSelective(orderCommentGoods);
        }else {
            orderCommentGoods.setId(orderCommentGoodsResult.getId());
            i = commentGoodsMapper.updateByPrimaryKeySelective(orderCommentGoods);
        }
        //落库order_list中  修改订单表中是否评论[0是为评论  1是已评论]
        ImsOrder serviceOrderList = new ImsOrder();
        serviceOrderList.setId(order.getId());
        serviceOrderList.setIsComment((byte) 1);
        i = orderListMapper.updateByPrimaryKeySelective(serviceOrderList);
        //根据星级进行判断是好评还是差评
        if (commentDto.getStar() < 2 || commentDto.getStar() > 3){
            ImsCoach coachInfo = coachListMapper.selectByPrimaryKey(order.getCoachId());
            Integer credit = coachInfo.getCredit();
            Integer afterPoints = 0;
            //记录信誉分操作记录
            ImsCreditDetail creditDetail = new ImsCreditDetail();
            creditDetail.setCoachId(coachInfo.getId());
            creditDetail.setPoints(ADD_CREDIT);
            creditDetail.setBeforePoints(credit);
            if (commentDto.getStar() > 3) {
                creditDetail.setCreditType((byte) 1);
                afterPoints = credit + ADD_CREDIT;
                creditDetail.setChangeReason((byte) 2);
            }
            if (commentDto.getStar() < 2) {
                creditDetail.setCreditType((byte) 2);
                afterPoints = credit - ADD_CREDIT;;
                creditDetail.setChangeReason((byte) 3);
            }
            creditDetail.setOrderId(order.getId());
            creditDetail.setAfterPoints(afterPoints);
            creditDetail.setRemark(commentDto.getText());
            creditDetail.setOperatorId(userId);
            creditDetail.setOperatorType((byte) 3);
            creditDetail.setCreateTime(new Date());
            creditDetailMapper.insertSelective(creditDetail);
            //对师傅信誉分进行修改
            coachInfo.setCredit(afterPoints);
            coachListMapper.updateByPrimaryKeySelective(coachInfo);
        }
        return i == 1 ? ResponseObject.ok("评价成功") : ResponseObject.fail("此订单评价失败，请联系管理员");
    }

    @Override
    public ResponseObject refundProgress(HttpServletRequest request, IdDto orderIdDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户：{}", userId);
        ImsRefundOrder refundOrder = refundOrderMapper.selectByOrderId(orderIdDto.getId());
        if (refundOrder == null) {
            return ResponseObject.fail("尚未查询到当前订单退款记录");
        }
        if (!refundOrder.getUserId().equals(userId)) {
            return ResponseObject.fail("你无权查看此进度");
        }
        RefundProgressVo refundProgressVo = new RefundProgressVo();
        BeanUtil.copyProperties(refundOrder, refundProgressVo);
        return ResponseObject.ok(refundProgressVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject updateDiffStatus(HttpServletRequest request, OrderDiffStatusVo diffStatusVo) {
        Integer userId = (Integer) request.getAttribute("userId");
        ImsOrderDiffPrice imsOrderDiffPrice = orderDiffPriceMapper.selectByPrimaryKey(diffStatusVo.getId());
        if (imsOrderDiffPrice == null){
            return ResponseObject.fail("尚未查询到当前加价申请，请与师傅确认");
        }
        if (!imsOrderDiffPrice.getUserId().equals(userId)){
            return ResponseObject.fail("当前订单不属于你，你无权进行此操作");
        }
        if (imsOrderDiffPrice.getStatus() == -1){
            return ResponseObject.fail("师傅取消了该次申请，请与师傅确认");
        }
        CoachLabelAndStatusVo coach = coachListMapper.selectLabelAndStatusByPrimaryKey(imsOrderDiffPrice.getCoachId());
        switch (diffStatusVo.getStatus()){
            case 1:
                ServiceConfigVo schoolConfig = (ServiceConfigVo) cacheService.get("schoolConfig");
                Object cachedObject = cacheService.get("imsCoachLabelsMap");
                Integer lastCommission = CalculateProportionUtils.coachProportion(coach.getLabelId(), schoolConfig, cachedObject);
                // 计算比例：(100 - lastCommission) / 100，保留2位小数（四舍五入）
                BigDecimal lastProportion = new BigDecimal(100 - lastCommission)
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                log.info("差价计算·······当前师傅得到的订单比例为：{}", lastProportion);
                BigDecimal multiply = imsOrderDiffPrice.getDiffAmount().multiply(lastProportion).setScale(2, RoundingMode.HALF_UP);
                imsOrderDiffPrice.setCreditPrice(multiply);
                imsOrderDiffPrice.setStatus((byte) 1);
                orderDiffPriceMapper.updateByPrimaryKeySelective(imsOrderDiffPrice);
                break;
            case 2:
                imsOrderDiffPrice.setStatus((byte) 3);
                imsOrderDiffPrice.setRefusedText(diffStatusVo.getText());
                orderDiffPriceMapper.updateByPrimaryKeySelective(imsOrderDiffPrice);
                break;
            default:
                return ResponseObject.fail("尚未匹配到合法操作项");
        }
        return ResponseObject.ok("操作成功",null);
    }

    @Override
    public ResponseObject payDiffPrice(HttpServletRequest request, PayDiffPriceDto payDiffPriceDto) {
        Integer userId = (Integer) request.getAttribute("userId");
        ImsUser userInfo = userListMapper.selectByPrimaryKey(userId);
        ImsOrderDiffPrice imsOrderDiffPrice = orderDiffPriceMapper.selectByPrimaryKey(payDiffPriceDto.getId());
        if (!imsOrderDiffPrice.getUserId().equals(userId)) {
            return ResponseObject.fail("该加价订单不属于你，你无权操作");
        }
        if(imsOrderDiffPrice.getStatus() == 2){
            return ResponseObject.fail("此订单已支付，无需重复支付");
        }
        ImsOrder orderInfo = orderListMapper.selectByPrimaryKey(imsOrderDiffPrice.getOrderId());
        if (orderInfo.getPayType() < 3){
            return ResponseObject.fail("当前订单状态不允许付款");
        }
        if (orderInfo.getRefundStatus() == 2 || orderInfo.getRefundStatus() == 1){
            return ResponseObject.fail("当前订单有申请退款操作，不可进行此操作");
        }
        //微信支付通道
        if (payDiffPriceDto.getType() == 1) {
            //构建支付参数
            WxAppPayReq req = new WxAppPayReq();
            req.setOpenId(userInfo.getWxOpenid());
            req.setOrderCode(imsOrderDiffPrice.getDiffCode());
            req.setPayPrice(imsOrderDiffPrice.getDiffAmount());
            req.setAttach("diffPayment");
            OrderPayVo vo = new OrderPayVo();
            try {
                UnifiedOrderRes unifiedOrderRes = payService.wxAppPay(req, request.getRemoteAddr());
                vo.setAppId(unifiedOrderRes.getAppid());
                long timestamp = System.currentTimeMillis() / 1000;
                vo.setTimestamp(timestamp);
                String nonceStr = payService.genNonceStr();
                vo.setNonceStr(nonceStr);
                vo.setPrepayId(unifiedOrderRes.getPrepayId());
                vo.setPackageValue("Sign=WXPay");
                vo.setPartnerId(mchId);

                //前端需要的sign仅需这几个  坑
                SortedMap<String, String> signParams = new TreeMap<>();
                signParams.put("appId", unifiedOrderRes.getAppid());
                signParams.put("timeStamp", String.valueOf(timestamp));
                signParams.put("nonceStr", nonceStr);
                signParams.put("package", "prepay_id=" + unifiedOrderRes.getPrepayId());
                signParams.put("signType", "MD5");
                String paySign = payService.buildSign(signParams);
                vo.setSign(paySign);
                log.info("支付调用成功----等待支付回调接口确认");
                //todo后续再添加差价支付记录日志
                log.info("用户：{} 缴纳订单差价:{},拼成的商户code是:{}", userId, imsOrderDiffPrice.getCreditPrice(), imsOrderDiffPrice.getDiffCode());
                return ResponseObject.ok(vo);
            } catch (Exception e) {
                log.error("支付失败", e);
                return ResponseObject.fail("支付失败", e.getMessage());
            }
        }
        return ResponseObject.fail("尚未当前适配的支付方式");
    }

    @Override
    @Transactional
    public boolean updateDiffPaymentOrderWithDiffOrderCode(String orderCode, String wxTransactionId, String totalFee) {
        try {
            BigDecimal applyPrice = new BigDecimal(totalFee).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            ImsOrderDiffPrice orderDiffPrice = orderDiffPriceMapper.selectByDiffCode(orderCode);
            if (orderDiffPrice == null){
                log.info("差价记录更新失败····未查询到订单号为：{}的差价记录",orderCode);
                throw new RuntimeException();
            }
            orderDiffPrice.setStatus((byte) 2);
            orderDiffPrice.setTransactionId(wxTransactionId);
            orderDiffPrice.setApplyPrice(applyPrice);
            orderDiffPriceMapper.updateByPrimaryKeySelective(orderDiffPrice);
//            ImsOrder orderInfo = orderListMapper.selectByPrimaryKey(orderDiffPrice.getOrderId());
//            //修改师傅服务金额
//            orderInfo.setCoachServicePrice(orderInfo.getCoachServicePrice().add(orderDiffPrice.getCreditPrice()));
//            orderListMapper.updateByPrimaryKeySelective(orderInfo);
            return true;
        }catch (Exception e){
            log.info("用户更新查询后的订单异常:{}",e.toString());
            return false;
        }
    }
}
