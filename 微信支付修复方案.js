// 微信支付签名修复方案

// 方案1：前端重新生成正确签名
function generateCorrectWeChatPaySign(params, apiKey) {
    const crypto = require('crypto');
    
    // 用于签名的参数（小写格式）
    const signParams = {
        appid: params.appId,
        noncestr: params.nonceStr,
        package: 'Sign=WXPay',
        partnerid: params.partnerId,
        prepayid: params.prepayId,
        timestamp: String(params.timestamp)
    };
    
    // 排序参数
    const sortedKeys = Object.keys(signParams).sort();
    const pairs = [];
    
    sortedKeys.forEach(key => {
        if (signParams[key] !== '' && signParams[key] !== null) {
            pairs.push(`${key}=${signParams[key]}`);
        }
    });
    
    const stringA = pairs.join('&');
    const stringSignTemp = stringA + '&key=' + apiKey;
    
    const sign = crypto.createHash('md5')
        .update(stringSignTemp, 'utf8')
        .digest('hex')
        .toUpperCase();
    
    console.log('签名字符串:', stringSignTemp);
    console.log('生成签名:', sign);
    
    return sign;
}

// 方案2：修复后的支付调用代码
function fixedConfirmPay() {
    this.$api.service.nowPay({
        orderId: this.id,
        couponId: this.confirmCou ? this.confirmCou.couponId : 0,
        type: 1,
    }).then(res => {
        if(res.code === '-1'){
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        } else {
            console.log('后端返回数据:', res);
            let obj = res.data;
            
            // 使用正确的API密钥重新生成签名
            const apiKey = '6ea19f1af5a476cd249adf884922c0b6';
            const correctSign = generateCorrectWeChatPaySign(obj, apiKey);
            
            console.log('后端签名:', obj.sign);
            console.log('正确签名:', correctSign);
            
            // 使用正确的签名调用支付
            uni.requestPayment({
                provider: 'wxpay',
                timeStamp: String(obj.timestamp),
                nonceStr: obj.nonceStr,
                package: "prepay_id=" + obj.prepayId,
                partnerid: obj.partnerId,
                signType: "MD5",
                paySign: correctSign, // 使用重新生成的正确签名
                appId: obj.appId,
                success: (res1) => {
                    console.log('支付成功', res1);
                    uni.showToast({
                        title: '支付成功',
                        icon: 'success'
                    });
                    this.dingyue();
                    setTimeout(() => {
                        uni.redirectTo({
                            url: '/user/order_list?tab=0'
                        });
                    }, 1000);
                },
                fail: (err) => {
                    console.error('支付失败:', err);
                    if (err.errMsg.includes('cancel')) {
                        uni.showToast({
                            title: '您已取消支付',
                            icon: 'none'
                        });
                    } else {
                        uni.showToast({
                            title: '支付失败，请稍后重试',
                            icon: 'none'
                        });
                    }
                }
            });
        }
    });
}

// 当前数据的正确签名
const currentData = {
    appId: "wx5e2f07d307654aed",
    nonceStr: "2b69e3594eb64ebfbcdab67bf4ec1848",
    packageValue: "Sign=WXPay",
    partnerId: "**********",
    prepayId: "wx03141651248204edbe831d850be5bc0000",
    timestamp: **********
};

const apiKey = '6ea19f1af5a476cd249adf884922c0b6';
const correctSign = generateCorrectWeChatPaySign(currentData, apiKey);

console.log('=== 当前数据的正确签名 ===');
console.log('后端返回签名:', 'ECDEEDDB1C62875AAE580DC07776C95F');
console.log('正确签名应该是:', correctSign);

// 正确的uni.requestPayment参数
const correctPaymentParams = {
    provider: 'wxpay',
    timeStamp: String(currentData.timestamp),
    nonceStr: currentData.nonceStr,
    package: "prepay_id=" + currentData.prepayId,
    partnerid: currentData.partnerId,
    signType: "MD5",
    paySign: correctSign,
    appId: currentData.appId
};

console.log('=== 正确的支付参数 ===');
console.log(JSON.stringify(correctPaymentParams, null, 2));
