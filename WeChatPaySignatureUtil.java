package com.ims.utils;

import org.apache.commons.codec.digest.DigestUtils;
import java.util.*;

/**
 * 微信支付APP调用签名生成工具类
 * 修复后的正确实现
 */
public class WeChatPaySignatureUtil {
    
    /**
     * 生成微信支付APP调用签名
     * @param params 支付参数
     * @param apiKey API密钥
     * @return 签名字符串
     */
    public static String generateAppPaySign(Map<String, String> params, String apiKey) {
        // 1. 过滤空值并排序参数（按ASCII码排序）
        Map<String, String> sortedParams = new TreeMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (value != null && !value.isEmpty() && !"sign".equals(key)) {
                sortedParams.put(key, value);
            }
        }
        
        // 2. 拼接字符串
        StringBuilder stringA = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            stringA.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        
        // 3. 拼接API密钥
        String stringSignTemp = stringA.toString() + "key=" + apiKey;
        
        // 4. MD5加密并转大写
        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();
        
        System.out.println("签名原始字符串: " + stringSignTemp);
        System.out.println("生成的签名: " + sign);
        
        return sign;
    }
    
    /**
     * 验证签名
     * @param params 包含签名的参数
     * @param apiKey API密钥
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, String> params, String apiKey) {
        String receivedSign = params.get("sign");
        if (receivedSign == null) {
            return false;
        }
        
        // 移除sign参数后重新计算签名
        Map<String, String> paramsWithoutSign = new HashMap<>(params);
        paramsWithoutSign.remove("sign");
        
        String calculatedSign = generateAppPaySign(paramsWithoutSign, apiKey);
        
        System.out.println("接收到的签名: " + receivedSign);
        System.out.println("计算出的签名: " + calculatedSign);
        
        return receivedSign.equals(calculatedSign);
    }
    
    /**
     * 为APP支付生成正确的签名参数
     * @param appId 应用ID
     * @param nonceStr 随机字符串
     * @param prepayId 预支付ID
     * @param partnerId 商户号
     * @param timestamp 时间戳
     * @param apiKey API密钥
     * @return 签名
     */
    public static String generateAppPaySignCorrect(String appId, String nonceStr, 
                                                  String prepayId, String partnerId, 
                                                  long timestamp, String apiKey) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);                    // 注意：小写
        params.put("noncestr", nonceStr);              // 注意：小写
        params.put("package", "Sign=WXPay");           // 注意：固定值
        params.put("partnerid", partnerId);            // 注意：小写
        params.put("prepayid", prepayId);              // 注意：小写
        params.put("timestamp", String.valueOf(timestamp)); // 注意：小写
        
        return generateAppPaySign(params, apiKey);
    }
    
    /**
     * 测试方法 - 验证当前数据
     */
    public static void testCurrentData() {
        String apiKey = "6ea19f1af5a476cd249adf884922c0b6";
        
        // 最新的后端数据
        Map<String, String> params = new HashMap<>();
        params.put("appid", "wx5e2f07d307654aed");
        params.put("noncestr", "60314386a74f4bc1ac7a36fa85f58cac");
        params.put("package", "Sign=WXPay");
        params.put("partnerid", "1648027588");
        params.put("prepayid", "wx03141651248204edbe831d850be5bc0000");
        params.put("timestamp", "1754206596");
        
        String correctSign = generateAppPaySign(params, apiKey);
        
        System.out.println("=== 测试结果 ===");
        System.out.println("后端返回签名: A5F3F8151C5BE151B38B27718CA5C1A7");
        System.out.println("正确签名应该是: " + correctSign);
        
        // 验证后端签名
        params.put("sign", "A5F3F8151C5BE151B38B27718CA5C1A7");
        boolean isValid = verifySign(params, apiKey);
        System.out.println("后端签名验证结果: " + isValid);
    }
    
    public static void main(String[] args) {
        testCurrentData();
    }
}
