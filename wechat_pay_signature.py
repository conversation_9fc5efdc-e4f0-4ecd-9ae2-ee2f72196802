import hashlib
from collections import OrderedDict

class WeChatPaySignature:
    """微信支付APP调用签名生成和验证"""
    
    def __init__(self, api_key):
        self.api_key = api_key  # APIv2密钥：6ea19f1af5a476cd249adf884922c0b6
    
    def generate_app_pay_sign(self, params):
        """
        生成APP调用微信支付的签名
        :param params: 支付参数字典
        :return: 签名字符串
        """
        # 1. 参数排序（按ASCII码排序）
        sorted_params = self.sort_params(params)
        
        # 2. 拼接字符串
        string_a = self.build_sign_string(sorted_params)
        
        # 3. 拼接API密钥
        string_sign_temp = string_a + '&key=' + self.api_key
        
        # 4. MD5加密并转大写
        sign = hashlib.md5(string_sign_temp.encode('utf-8')).hexdigest().upper()
        
        print(f'签名原始字符串: {string_sign_temp}')
        print(f'生成的签名: {sign}')
        
        return sign
    
    def verify_sign(self, params):
        """
        验证签名
        :param params: 包含签名的参数字典
        :return: 验证结果
        """
        received_sign = params.get('sign')
        params_copy = params.copy()
        if 'sign' in params_copy:
            del params_copy['sign']
        
        calculated_sign = self.generate_app_pay_sign(params_copy)
        
        print(f'接收到的签名: {received_sign}')
        print(f'计算出的签名: {calculated_sign}')
        
        return received_sign == calculated_sign
    
    def sort_params(self, params):
        """
        参数排序
        :param params: 参数字典
        :return: 排序后的参数字典
        """
        sorted_params = OrderedDict()
        
        # 按ASCII码排序
        for key in sorted(params.keys()):
            value = params[key]
            # 过滤空值
            if value is not None and value != '':
                sorted_params[key] = str(value)
        
        return sorted_params
    
    def build_sign_string(self, params):
        """
        构建签名字符串
        :param params: 排序后的参数字典
        :return: 签名字符串
        """
        pairs = []
        
        for key, value in params.items():
            if key != 'sign':
                pairs.append(f'{key}={value}')
        
        return '&'.join(pairs)

# 使用示例
if __name__ == '__main__':
    api_key = '6ea19f1af5a476cd249adf884922c0b6'
    wechat_pay = WeChatPaySignature(api_key)
    
    # 您提供的数据
    pay_params = {
        'appid': 'wx5e2f07d307654aed',
        'noncestr': 'a0e66dc82a4e484d8e53df5b589a9351',
        'package': 'Sign=WXPay',
        'partnerid': '1648027588',
        'prepayid': 'wx03141651248204edbe831d850be5bc0000',
        'timestamp': '1754204368'
    }
    
    print('=== 生成新签名 ===')
    new_sign = wechat_pay.generate_app_pay_sign(pay_params)
    
    print('\n=== 验证原签名 ===')
    params_with_sign = pay_params.copy()
    params_with_sign['sign'] = 'A99F86E353DE2EBC0F9F02BEACAAE689'
    
    is_valid = wechat_pay.verify_sign(params_with_sign)
    print(f'签名验证结果: {is_valid}')
    
    # 输出最终的APP调用参数
    print('\n=== APP调用参数 ===')
    final_params = {
        'appid': pay_params['appid'],
        'partnerid': pay_params['partnerid'],
        'prepayid': pay_params['prepayid'],
        'package': pay_params['package'],
        'noncestr': pay_params['noncestr'],
        'timestamp': pay_params['timestamp'],
        'sign': new_sign
    }
    
    for key, value in final_params.items():
        print(f'{key}: {value}')
