const crypto = require('crypto');

/**
 * 微信支付APP调用签名生成和验证
 */
class WeChatPaySignature {
    constructor(apiKey) {
        this.apiKey = apiKey; // APIv2密钥：6ea19f1af5a476cd249adf884922c0b6
    }

    /**
     * 生成APP调用微信支付的签名
     * @param {Object} params 支付参数
     * @returns {string} 签名
     */
    generateAppPaySign(params) {
        // 1. 参数排序（按ASCII码排序）
        const sortedParams = this.sortParams(params);
        
        // 2. 拼接字符串
        const stringA = this.buildSignString(sortedParams);
        
        // 3. 拼接API密钥
        const stringSignTemp = stringA + '&key=' + this.apiKey;
        
        // 4. MD5加密并转大写
        const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
        
        console.log('签名原始字符串:', stringSignTemp);
        console.log('生成的签名:', sign);
        
        return sign;
    }

    /**
     * 验证签名
     * @param {Object} params 包含签名的参数
     * @returns {boolean} 验证结果
     */
    verifySign(params) {
        const receivedSign = params.sign;
        delete params.sign; // 删除sign参数
        
        const calculatedSign = this.generateAppPaySign(params);
        
        console.log('接收到的签名:', receivedSign);
        console.log('计算出的签名:', calculatedSign);
        
        return receivedSign === calculatedSign;
    }

    /**
     * 参数排序
     * @param {Object} params 参数对象
     * @returns {Object} 排序后的参数
     */
    sortParams(params) {
        const sortedKeys = Object.keys(params).sort();
        const sortedParams = {};
        
        sortedKeys.forEach(key => {
            if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                sortedParams[key] = params[key];
            }
        });
        
        return sortedParams;
    }

    /**
     * 构建签名字符串
     * @param {Object} params 排序后的参数
     * @returns {string} 签名字符串
     */
    buildSignString(params) {
        const pairs = [];
        
        Object.keys(params).forEach(key => {
            if (key !== 'sign') {
                pairs.push(`${key}=${params[key]}`);
            }
        });
        
        return pairs.join('&');
    }
}

// 使用示例
const apiKey = '6ea19f1af5a476cd249adf884922c0b6';
const wechatPay = new WeChatPaySignature(apiKey);

// 您提供的数据
const payParams = {
    appid: 'wx5e2f07d307654aed',
    noncestr: 'a0e66dc82a4e484d8e53df5b589a9351',
    package: 'Sign=WXPay',
    partnerid: '1648027588',
    prepayid: 'wx03141651248204edbe831d850be5bc0000',
    timestamp: '1754204368'
};

console.log('=== 生成新签名 ===');
const newSign = wechatPay.generateAppPaySign(payParams);

console.log('\n=== 验证原签名 ===');
const paramsWithSign = {
    ...payParams,
    sign: 'A99F86E353DE2EBC0F9F02BEACAAE689'
};

const isValid = wechatPay.verifySign({...paramsWithSign});
console.log('签名验证结果:', isValid);

// 输出最终的APP调用参数
console.log('\n=== APP调用参数 ===');
console.log({
    appid: payParams.appid,
    partnerid: payParams.partnerid,
    prepayid: payParams.prepayid,
    package: payParams.package,
    noncestr: payParams.noncestr,
    timestamp: payParams.timestamp,
    sign: newSign
});

module.exports = WeChatPaySignature;
