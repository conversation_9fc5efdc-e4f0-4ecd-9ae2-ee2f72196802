const crypto = require('crypto');

/**
 * 微信支付APP调用签名生成和验证
 */
class WeChatPaySignature {
    constructor(apiKey) {
        this.apiKey = apiKey; // APIv2密钥：6ea19f1af5a476cd249adf884922c0b6
    }

    /**
     * 生成APP调用微信支付的签名
     * @param {Object} params 支付参数
     * @returns {string} 签名
     */
    generateAppPaySign(params) {
        // 1. 参数排序（按ASCII码排序）
        const sortedParams = this.sortParams(params);
        
        // 2. 拼接字符串
        const stringA = this.buildSignString(sortedParams);
        
        // 3. 拼接API密钥
        const stringSignTemp = stringA + '&key=' + this.apiKey;
        
        // 4. MD5加密并转大写
        const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
        
        console.log('签名原始字符串:', stringSignTemp);
        console.log('生成的签名:', sign);
        
        return sign;
    }

    /**
     * 验证签名
     * @param {Object} params 包含签名的参数
     * @returns {boolean} 验证结果
     */
    verifySign(params) {
        const receivedSign = params.sign;
        delete params.sign; // 删除sign参数
        
        const calculatedSign = this.generateAppPaySign(params);
        
        console.log('接收到的签名:', receivedSign);
        console.log('计算出的签名:', calculatedSign);
        
        return receivedSign === calculatedSign;
    }

    /**
     * 参数排序
     * @param {Object} params 参数对象
     * @returns {Object} 排序后的参数
     */
    sortParams(params) {
        const sortedKeys = Object.keys(params).sort();
        const sortedParams = {};
        
        sortedKeys.forEach(key => {
            if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                sortedParams[key] = params[key];
            }
        });
        
        return sortedParams;
    }

    /**
     * 构建签名字符串
     * @param {Object} params 排序后的参数
     * @returns {string} 签名字符串
     */
    buildSignString(params) {
        const pairs = [];
        
        Object.keys(params).forEach(key => {
            if (key !== 'sign') {
                pairs.push(`${key}=${params[key]}`);
            }
        });
        
        return pairs.join('&');
    }
}

// 使用示例
const apiKey = '6ea19f1af5a476cd249adf884922c0b6';
const wechatPay = new WeChatPaySignature(apiKey);

// 测试不同的参数名称格式

// 验证最新的后端返回数据
const latestPayParams = {
    appid: 'wx5e2f07d307654aed',
    noncestr: '60314386a74f4bc1ac7a36fa85f58cac',
    package: 'Sign=WXPay',
    partnerid: '1648027588',
    prepayid: 'wx03141651248204edbe831d850be5bc0000',
    timestamp: '1754205919' // 注意：您没有提供新的timestamp，使用之前的
};

console.log('=== 验证最新后端数据 ===');
const latestSign = wechatPay.generateAppPaySign(latestPayParams);

console.log('\n=== 验证后端签名 ===');
const latestParamsWithSign = {
    ...latestPayParams,
    sign: 'A5F3F8151C5BE151B38B27718CA5C1A7'
};
const latestIsValid = wechatPay.verifySign({...latestParamsWithSign});

console.log('\n=== 最新数据验证结果 ===');
console.log('后端返回签名:', 'A5F3F8151C5BE151B38B27718CA5C1A7');
console.log('正确签名应该是:', latestSign);
console.log('签名验证结果:', latestIsValid);

// 测试不同的timestamp值
console.log('\n=== 测试可能的timestamp问题 ===');
const timestampTests = [
    String(Date.now()).substring(0, 10), // 当前时间戳
    '1754205919', // 之前的值
    Math.floor(Date.now() / 1000).toString() // 当前秒级时间戳
];

timestampTests.forEach((ts, index) => {
    const testParams = {
        ...latestPayParams,
        timestamp: ts
    };
    const testSign = wechatPay.generateAppPaySign(testParams);
    console.log(`测试${index + 1} - timestamp: ${ts}, 签名: ${testSign}`);
    if (testSign === 'A5F3F8151C5BE151B38B27718CA5C1A7') {
        console.log(`✅ 找到匹配的timestamp: ${ts}`);
    }
});



module.exports = WeChatPaySignature;
