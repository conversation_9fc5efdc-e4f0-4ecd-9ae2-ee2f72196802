const crypto = require('crypto');

/**
 * 微信支付APP调用签名生成和验证
 */
class WeChatPaySignature {
    constructor(apiKey) {
        this.apiKey = apiKey; // APIv2密钥：6ea19f1af5a476cd249adf884922c0b6
    }

    /**
     * 生成APP调用微信支付的签名
     * @param {Object} params 支付参数
     * @returns {string} 签名
     */
    generateAppPaySign(params) {
        // 1. 参数排序（按ASCII码排序）
        const sortedParams = this.sortParams(params);
        
        // 2. 拼接字符串
        const stringA = this.buildSignString(sortedParams);
        
        // 3. 拼接API密钥
        const stringSignTemp = stringA + '&key=' + this.apiKey;
        
        // 4. MD5加密并转大写
        const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
        
        console.log('签名原始字符串:', stringSignTemp);
        console.log('生成的签名:', sign);
        
        return sign;
    }

    /**
     * 验证签名
     * @param {Object} params 包含签名的参数
     * @returns {boolean} 验证结果
     */
    verifySign(params) {
        const receivedSign = params.sign;
        delete params.sign; // 删除sign参数
        
        const calculatedSign = this.generateAppPaySign(params);
        
        console.log('接收到的签名:', receivedSign);
        console.log('计算出的签名:', calculatedSign);
        
        return receivedSign === calculatedSign;
    }

    /**
     * 参数排序
     * @param {Object} params 参数对象
     * @returns {Object} 排序后的参数
     */
    sortParams(params) {
        const sortedKeys = Object.keys(params).sort();
        const sortedParams = {};
        
        sortedKeys.forEach(key => {
            if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                sortedParams[key] = params[key];
            }
        });
        
        return sortedParams;
    }

    /**
     * 构建签名字符串
     * @param {Object} params 排序后的参数
     * @returns {string} 签名字符串
     */
    buildSignString(params) {
        const pairs = [];
        
        Object.keys(params).forEach(key => {
            if (key !== 'sign') {
                pairs.push(`${key}=${params[key]}`);
            }
        });
        
        return pairs.join('&');
    }
}

// 使用示例
const apiKey = '6ea19f1af5a476cd249adf884922c0b6';
const wechatPay = new WeChatPaySignature(apiKey);

// 测试不同的参数名称格式

// 最新的支付数据验证
const payParamsCamelCase = {
    appId: 'wx5e2f07d307654aed',
    nonceStr: '2b69e3594eb64ebfbcdab67bf4ec1848',
    packageValue: 'Sign=WXPay',
    partnerId: '1648027588',
    prepayId: 'wx03141651248204edbe831d850be5bc0000',
    timestamp: '1754205919'
};

// 2. 标准小写格式
const payParamsLowerCase = {
    appid: 'wx5e2f07d307654aed',
    noncestr: '2b69e3594eb64ebfbcdab67bf4ec1848',
    package: 'Sign=WXPay',
    partnerid: '1648027588',
    prepayid: 'wx03141651248204edbe831d850be5bc0000',
    timestamp: '1754205919'
};

console.log('=== 测试驼峰格式参数名称 ===');
const signCamelCase = wechatPay.generateAppPaySign(payParamsCamelCase);

console.log('\n=== 测试小写格式参数名称 ===');
const signLowerCase = wechatPay.generateAppPaySign(payParamsLowerCase);

console.log('\n=== 验证原签名（驼峰格式） ===');
const paramsWithSignCamel = {
    ...payParamsCamelCase,
    sign: 'ECDEEDDB1C62875AAE580DC07776C95F'
};
const isValidCamel = wechatPay.verifySign({...paramsWithSignCamel});

console.log('\n=== 验证原签名（小写格式） ===');
const paramsWithSignLower = {
    ...payParamsLowerCase,
    sign: 'ECDEEDDB1C62875AAE580DC07776C95F'
};

const isValidLower = wechatPay.verifySign({...paramsWithSignLower});

console.log('\n=== 结果对比 ===');
console.log('驼峰格式签名:', signCamelCase);
console.log('小写格式签名:', signLowerCase);
console.log('后端返回签名:', 'ECDEEDDB1C62875AAE580DC07776C95F');
console.log('驼峰格式验证:', isValidCamel);
console.log('小写格式验证:', isValidLower);

// 输出最终的APP调用参数
console.log('\n=== 正确的APP调用参数 ===');
console.log({
    appid: payParamsLowerCase.appid,
    partnerid: payParamsLowerCase.partnerid,
    prepayid: payParamsLowerCase.prepayid,
    package: payParamsLowerCase.package,
    noncestr: payParamsLowerCase.noncestr,
    timestamp: payParamsLowerCase.timestamp,
    sign: signLowerCase
});

module.exports = WeChatPaySignature;
